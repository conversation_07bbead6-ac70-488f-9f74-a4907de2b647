#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试订单模块语法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    print("导入PyQt6...")
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import Qt
    
    print("导入订单模块...")
    from ui.modules.order_module import OrderModule
    
    print("创建应用...")
    app = QApplication([])
    
    print("创建订单模块...")
    module = OrderModule()
    
    print("✅ 所有导入和创建成功！")
    print("界面布局进一步优化完成，主要改进：")
    print("1. 标题字体从18px减小到16px，进一步减少空间")
    print("2. 整体边距从15px减小到10px，间距从10px减小到5px")
    print("3. 筛选区域padding从1.5rem减小到8px，间距从15px减小到8px")
    print("4. 按钮高度限制为32px，padding减小到8px 16px")
    print("5. 进度条高度从8px减小到6px")
    print("6. 主内容区域使用stretch factor占用大部分空间")
    print("7. 表格标题字体从18px减小到16px")
    print("8. 订单列表和详情区域获得更多垂直空间")
    
    app.quit()
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
