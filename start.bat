@echo off
echo ====================================
echo 智慧食堂管理系统启动脚本
echo Smart Canteen Management System
echo ====================================

cd /d "%~dp0"

echo 检查Python环境...
python --version
if errorlevel 1 (
    echo Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo 检查依赖包...
python -c "import PyQt6; print('PyQt6 OK')"
if errorlevel 1 (
    echo 正在安装PyQt6...
    pip install PyQt6
)

python -c "import requests; print('requests OK')"
if errorlevel 1 (
    echo 正在安装requests...
    pip install requests
)

echo 启动程序...
echo 尝试使用修复版启动脚本...
python start_fixed.py

if errorlevel 1 (
    echo 修复版启动失败，尝试原版...
    python main.py
)

echo 程序已退出
pause
