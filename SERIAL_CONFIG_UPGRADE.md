# 串口配置升级说明

## 概述

本次升级解决了重量获取串口固定为COM4的限制，现在支持配置任意串口，并提供自动检测功能。

## 主要改进

### 1. 配置系统升级

**文件**: `config/settings.py`, `config/app_config.json`

- 新增串口配置项：
  - `serial.port`: 串口名称（默认COM4）
  - `serial.baudrate`: 波特率（默认9600）
  - `serial.timeout`: 超时时间（默认1秒）
  - `serial.auto_detect`: 自动检测开关（默认启用）

### 2. 串口工作线程增强

**文件**: `ui/modules/weight_submission_module.py`

- `SerialWorker`类支持可配置参数
- 新增`detect_weight_scale_port()`方法实现自动检测
- 支持检测过程中的状态反馈
- 动态显示当前使用的串口

### 3. 串口配置界面

**文件**: `ui/dialogs/serial_config_dialog.py`

新增专用的串口配置对话框，包含：
- 串口选择（下拉列表 + 手动输入）
- 波特率设置（常用值 + 自定义）
- 超时时间配置
- 自动检测开关
- 设备检测功能（扫描所有串口）
- 连接测试功能
- 配置保存功能

### 4. 主界面集成

**文件**: `ui/main_window.py`

- 侧边栏新增"⚙️ 系统设置"按钮
- 设置对话框包含串口配置选项卡
- 提供便捷的配置入口

## 功能特性

### 自动检测原理

1. 扫描所有可用串口
2. 尝试连接每个串口
3. 读取数据并检查是否符合重量数据格式
4. 格式匹配: `/^[sw][gn](-?\d+\.?\d*)kg$/i`
5. 自动选择检测到的第一个重量秤设备

### 配置管理

- 支持运行时配置修改
- 配置自动保存到JSON文件
- 重启应用后生效
- 向后兼容现有配置

### 用户界面

- 友好的图形化配置界面
- 实时串口检测和状态显示
- 连接测试和错误提示
- 进度条显示检测过程

## 使用方法

### 基本配置

1. 启动应用
2. 点击侧边栏"⚙️ 系统设置"按钮
3. 选择"串口设置"选项卡
4. 点击"串口配置"按钮

### 手动配置

1. 在串口下拉列表中选择目标端口
2. 设置合适的波特率（通常9600）
3. 调整超时时间（1-10秒）
4. 点击"测试连接"验证
5. 保存配置

### 自动检测

1. 勾选"启用自动检测重量秤设备"
2. 点击"检测重量秤设备"按钮
3. 等待扫描完成
4. 系统会自动选择检测到的设备
5. 保存配置

## 技术细节

### 检测算法

```python
def detect_weight_scale_port(self):
    """自动检测重量秤串口"""
    ports = list(serial.tools.list_ports.comports())
    
    for port in ports:
        try:
            test_serial = serial.Serial(port.device, 9600, timeout=0.5)
            time.sleep(1.5)  # 等待数据
            
            if test_serial.in_waiting > 0:
                data = test_serial.read(test_serial.in_waiting).decode('utf-8', errors='ignore')
                
                # 检查重量数据格式
                if re.search(r'[sw][gn](-?\d+\.?\d*)kg', data, re.IGNORECASE):
                    return port.device
                    
        except Exception:
            continue
    
    return None
```

### 配置结构

```json
{
  "serial": {
    "port": "COM4",
    "baudrate": 9600,
    "timeout": 1,
    "auto_detect": true
  }
}
```

## 兼容性

- 向后兼容：未配置串口时默认使用COM4
- 支持Windows串口命名（COM1, COM2, ...）
- 支持自定义串口名称
- 兼容现有的重量数据格式

## 测试验证

提供了以下测试脚本：
- `test_serial_config.py`: 完整功能测试
- `verify_serial_config.py`: 配置验证
- `demo_serial_config.py`: 功能演示

## 部署说明

### 开发环境

所有文件已更新，包括：
- 主配置文件
- 源代码文件
- 测试脚本

### 发布环境

已同步更新release目录：
- `release/config/settings.py`
- `release/config/app_config.json`

### 依赖要求

确保安装pyserial库：
```bash
pip install pyserial
```

## 故障排除

### 常见问题

1. **串口检测失败**
   - 检查pyserial库是否安装
   - 确认串口设备已连接
   - 检查串口权限

2. **连接测试失败**
   - 验证串口名称是否正确
   - 检查波特率设置
   - 确认设备未被其他程序占用

3. **自动检测无结果**
   - 确保重量秤正在发送数据
   - 检查数据格式是否符合预期
   - 尝试手动配置已知端口

### 调试方法

1. 查看应用日志中的串口连接信息
2. 使用"检测重量秤设备"功能查看扫描结果
3. 使用"测试连接"验证配置正确性

## 总结

本次升级彻底解决了串口配置的灵活性问题，用户现在可以：

- 使用任意串口连接重量秤设备
- 通过友好的界面进行配置
- 利用自动检测功能简化设置过程
- 享受更好的用户体验和系统兼容性

升级后的系统更加灵活、易用，能够适应不同的硬件环境和用户需求。
