#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
入库API使用示例
Stock In API Usage Example
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.stock_in_api import StockInAPI
from datetime import datetime


class StockInManager:
    """入库管理器"""
    
    def __init__(self, username: str = None, password: str = None):
        """
        初始化入库管理器
        
        Args:
            username: 用户名
            password: 密码
        """
        self.api = StockInAPI()
        self.username = username
        self.password = password
        self.is_logged_in = False
    
    def login(self) -> bool:
        """
        登录系统
        
        Returns:
            是否登录成功
        """
        if not self.username or not self.password:
            print("错误: 用户名或密码未设置")
            return False
        
        result = self.api.login(self.username, self.password)
        if result['code'] == 200:
            self.is_logged_in = True
            print("登录成功!")
            return True
        else:
            print(f"登录失败: {result['msg']}")
            return False
    
    def get_products(self) -> list:
        """
        获取货品列表
        
        Returns:
            货品列表
        """
        if not self.is_logged_in:
            print("错误: 请先登录")
            return []
        
        result = self.api.get_products_list()
        if result['code'] == 200:
            return result.get('data', [])
        else:
            print(f"获取货品列表失败: {result['msg']}")
            return []
    
    def get_suppliers(self) -> list:
        """
        获取供应商列表
        
        Returns:
            供应商列表
        """
        if not self.is_logged_in:
            print("错误: 请先登录")
            return []
        
        result = self.api.get_suppliers_list()
        if result['code'] == 200:
            return result.get('data', [])
        else:
            print(f"获取供应商列表失败: {result['msg']}")
            return []
    
    def create_stock_in_order(self, depot_code: str, company_code: str, 
                             items: list) -> dict:
        """
        创建入库单
        
        Args:
            depot_code: 仓库代码
            company_code: 供应商代码
            items: 入库商品列表，每个商品包含：
                - product_code: 商品代码
                - quantity: 数量
                - unit: 单位
                - price: 价格
                - images: 图片路径列表（可选）
        
        Returns:
            创建结果
        """
        if not self.is_logged_in:
            return {'success': False, 'message': '请先登录'}
        
        # 获取入库单号
        index_result = self.api.get_stock_in_index()
        if index_result['code'] != 200:
            return {'success': False, 'message': f'获取入库单号失败: {index_result["msg"]}'}
        
        stock_in_code = index_result['msg']
        
        # 创建入库明细
        details = []
        for item in items:
            # 处理图片路径
            image_paths = item.get('images', [])
            path_str = ';'.join(image_paths) if image_paths else ''
            
            detail = self.api.create_stock_in_detail(
                code=item['product_code'],
                quantity=str(item['quantity']),
                unit=item['unit'],
                price=str(item['price']),
                path=path_str
            )
            details.append(detail)
        
        # 获取当前日期
        current_date = datetime.now().strftime('%Y-%m-%d')
        
        # 验证数据
        validation = self.api.validate_stock_in_data(
            code=stock_in_code,
            datetime=current_date,
            depot_code=depot_code,
            company_code=company_code,
            details=details
        )
        
        if not validation['valid']:
            return {
                'success': False, 
                'message': '数据验证失败',
                'errors': validation['errors']
            }
        
        # 提交入库申请
        submit_result = self.api.submit_stock_in(
            code=stock_in_code,
            datetime=current_date,
            depot_code=depot_code,
            company_code=company_code,
            details=details
        )
        
        if submit_result['code'] == 200:
            # 生成摘要
            summary = self.api.format_stock_in_summary(
                code=stock_in_code,
                datetime=current_date,
                depot_code=depot_code,
                company_code=company_code,
                details=details
            )
            
            return {
                'success': True,
                'message': '入库成功',
                'stock_in_code': stock_in_code,
                'summary': summary
            }
        else:
            return {
                'success': False,
                'message': f'入库失败: {submit_result["msg"]}'
            }


def example_usage():
    """使用示例"""
    
    print("=== 入库API使用示例 ===\n")
    
    # 创建入库管理器
    manager = StockInManager()
    
    # 注意：实际使用时需要提供真实的用户名和密码
    # manager = StockInManager("your_username", "your_password")
    # if not manager.login():
    #     return
    
    print("1. 模拟登录成功")
    manager.is_logged_in = True
    manager.api.set_access_token("mock_token")  # 模拟token
    
    print("\n2. 获取货品列表")
    print("products = manager.get_products()")
    print("# 返回货品列表，包含code、name、unit字段")
    
    print("\n3. 获取供应商列表")
    print("suppliers = manager.get_suppliers()")
    print("# 返回供应商列表，包含code、name字段")
    
    print("\n4. 创建入库单")
    items = [
        {
            'product_code': '13010405',
            'quantity': 10.5,
            'unit': '市斤',
            'price': 5.50,
            'images': ['images/product1.jpg', 'images/product2.jpg']
        },
        {
            'product_code': '13010406',
            'quantity': 8.0,
            'unit': '市斤',
            'price': 6.80,
            'images': ['images/product3.jpg']
        }
    ]
    
    print("items = [")
    for item in items:
        print(f"    {item},")
    print("]")
    
    print("\nresult = manager.create_stock_in_order(")
    print("    depot_code='S01',")
    print("    company_code='S001',")
    print("    items=items")
    print(")")
    
    print("\nif result['success']:")
    print("    print(f'入库成功! 单号: {result[\"stock_in_code\"]}')")
    print("    print(result['summary'])")
    print("else:")
    print("    print(f'入库失败: {result[\"message\"]}')")
    print("    if 'errors' in result:")
    print("        for error in result['errors']:")
    print("            print(f'  - {error}')")


if __name__ == "__main__":
    example_usage()
