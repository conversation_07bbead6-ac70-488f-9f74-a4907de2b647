#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局串口管理器 - 解决串口资源冲突问题
"""

from typing import Dict, Callable, Optional
import threading

# 简化版本，不依赖PyQt6
class DummySignal:
    def connect(self, func):
        pass
    def emit(self, *args):
        pass

class GlobalSerialManager:
    """全局串口管理器 - 单例模式"""

    # 信号（简化版本）
    weight_received = DummySignal()
    connection_status_changed = DummySignal()
    error_occurred = DummySignal()
    port_detected = DummySignal()
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized') and self._initialized:
            return

        self.serial_worker = None
        self.is_connected = False
        self.current_port = None
        self.subscribers = {}  # 订阅者字典 {module_name: callback_dict}
        self.mutex = threading.Lock()  # 使用标准库的锁
        self._initialized = True

        print("🔧 全局串口管理器初始化完成")
    
    def add_subscriber(self, module_name: str, callbacks: Dict[str, Callable]):
        """添加订阅者"""
        with self.mutex:
            self._add_subscriber_impl(module_name, callbacks)

    def _add_subscriber_impl(self, module_name: str, callbacks: Dict[str, Callable]):
        """添加订阅者实现"""
        self.subscribers[module_name] = callbacks
        print(f"📝 添加订阅者: {module_name}, 当前订阅者数量: {len(self.subscribers)}")

        # 如果已连接，立即通知新订阅者
        if self.is_connected:
            if 'connection_status' in callbacks:
                try:
                    callbacks['connection_status'](True)
                except Exception as e:
                    print(f"警告: 通知订阅者失败: {e}")
    
    def remove_subscriber(self, module_name: str):
        """移除订阅者"""
        with self.mutex:
            self._remove_subscriber_impl(module_name)

    def _remove_subscriber_impl(self, module_name: str):
        """移除订阅者实现"""
        if module_name in self.subscribers:
            del self.subscribers[module_name]
            print(f"🗑️ 移除订阅者: {module_name}, 剩余订阅者数量: {len(self.subscribers)}")

            # 如果没有订阅者了，断开连接
            if not self.subscribers and self.is_connected:
                print("📴 无订阅者，自动断开串口连接")
                self.stop_connection()
    
    def start_connection(self, force_restart=False):
        """启动串口连接"""
        with self.mutex:
            return self._start_connection_impl(force_restart)

    def _start_connection_impl(self, force_restart=False):
        """启动串口连接实现"""
        if self.is_connected and not force_restart:
            print("✅ 串口已连接，通知所有订阅者")
            self._notify_all_subscribers('connection_status', True)
            return True

        try:
            # 停止现有连接
            if self.serial_worker:
                print("🔄 停止现有串口连接")
                self.serial_worker.stop()
                self.serial_worker = None

            # 导入串口工作类
            from ui.modules.weight_submission_module import SerialWorker
            from config.settings import settings

            # 获取配置
            port = settings.serial_port
            baudrate = settings.serial_baudrate
            timeout = settings.serial_timeout
            auto_detect = settings.serial_auto_detect

            print(f"🚀 启动串口连接: port={port}, baudrate={baudrate}, auto_detect={auto_detect}")

            # 创建串口工作线程
            self.serial_worker = SerialWorker(port, baudrate, timeout, auto_detect)

            # 连接信号
            if hasattr(self.serial_worker, 'weight_received'):
                self.serial_worker.weight_received.connect(self._on_weight_received)
            if hasattr(self.serial_worker, 'error_occurred'):
                self.serial_worker.error_occurred.connect(self._on_error_occurred)
            if hasattr(self.serial_worker, 'connection_status'):
                self.serial_worker.connection_status.connect(self._on_connection_status_changed)
            if hasattr(self.serial_worker, 'port_detected'):
                self.serial_worker.port_detected.connect(self._on_port_detected)

            # 启动
            self.serial_worker.start()

            print(f"✅ 串口连接启动成功，订阅者数量: {len(self.subscribers)}")
            return True

        except Exception as e:
            error_msg = f"启动串口连接失败: {str(e)}"
            print(f"❌ {error_msg}")
            self._notify_all_subscribers('error_occurred', error_msg)
            return False
    
    def stop_connection(self):
        """停止串口连接"""
        with self.mutex:
            self._stop_connection_impl()

    def _stop_connection_impl(self):
        """停止串口连接实现"""
        if self.serial_worker:
            print("🔌 停止串口连接")
            try:
                self.serial_worker.stop()
            except Exception as e:
                print(f"警告: 停止串口工作线程失败: {e}")
            self.serial_worker = None

        self.is_connected = False
        self.current_port = None
        self._notify_all_subscribers('connection_status', False)
    
    def get_status(self):
        """获取连接状态"""
        return {
            'connected': self.is_connected,
            'port': self.current_port,
            'subscriber_count': len(self.subscribers),
            'subscribers': list(self.subscribers.keys())
        }
    
    def _on_weight_received(self, weight: str):
        """重量数据接收处理"""
        self._notify_all_subscribers('weight_received', weight)
    
    def _on_error_occurred(self, error: str):
        """错误处理"""
        print(f"🔴 串口错误: {error}")
        self._notify_all_subscribers('error_occurred', error)
    
    def _on_connection_status_changed(self, connected: bool):
        """连接状态变化处理"""
        self.is_connected = connected
        if connected and self.serial_worker:
            self.current_port = getattr(self.serial_worker, 'port', None)
            print(f"✅ 串口连接成功: {self.current_port}")
        else:
            self.current_port = None
            print("📴 串口连接断开")
        
        self._notify_all_subscribers('connection_status', connected)
    
    def _on_port_detected(self, port: str):
        """端口检测处理"""
        print(f"🔍 检测到端口: {port}")
        self.current_port = port
        self._notify_all_subscribers('port_detected', port)
    
    def _notify_all_subscribers(self, event_type: str, data):
        """通知所有订阅者"""
        for module_name, callbacks in self.subscribers.items():
            if event_type in callbacks:
                try:
                    callbacks[event_type](data)
                except Exception as e:
                    print(f"❌ 通知订阅者 {module_name} 失败: {e}")

# 创建全局实例
global_serial_manager = GlobalSerialManager()
