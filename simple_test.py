#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    try:
        print("🔍 测试基础导入...")
        from PyQt6.QtWidgets import QApplication, QWidget
        print("✅ PyQt6 导入成功")
        
        print("🔍 测试API导入...")
        from api.stock_api import StockAPI
        print("✅ StockAPI 导入成功")
        
        print("🔍 测试样式导入...")
        from ui.styles import GlassmorphismStyles
        print("✅ GlassmorphismStyles 导入成功")
        
        print("🔍 测试出库模块导入...")
        from ui.modules.stock_out_module import StockOutModule
        print("✅ StockOutModule 导入成功")
        
        print("🎉 所有导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_module_creation():
    """测试模块创建"""
    try:
        print("🔍 测试模块创建...")
        
        # 创建QApplication（GUI必需）
        app = QApplication([])
        
        # 导入模块
        from ui.modules.stock_out_module import StockOutModule
        
        # 创建模块实例
        module = StockOutModule()
        print("✅ StockOutModule 实例创建成功")
        
        # 检查基本属性
        if hasattr(module, 'depot_combo'):
            print("✅ 仓库选择组件存在")
        if hasattr(module, 'project_combo'):
            print("✅ 项目选择组件存在")
        if hasattr(module, 'stock_table'):
            print("✅ 库存表格组件存在")
        if hasattr(module, 'selected_table'):
            print("✅ 选中商品表格组件存在")
            
        print("🎉 模块创建测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 模块创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始出库管理模块测试")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("❌ 导入测试失败，退出")
        return
    
    print("\n" + "=" * 50)
    
    # 测试模块创建
    if not test_module_creation():
        print("❌ 模块创建测试失败，退出")
        return
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！出库管理模块可以正常使用。")

if __name__ == "__main__":
    main()
