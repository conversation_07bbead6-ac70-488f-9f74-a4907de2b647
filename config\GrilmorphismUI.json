{"designSystem": {"name": "Glassmorphism UI Design System", "version": "1.0.0", "description": "Complete design system for creating glassmorphism UI components", "coreStyles": {"glassmorphism": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "16px", "boxShadow": "0 8px 32px rgba(0, 0, 0, 0.1)"}, "glassmorphismDark": {"background": "rgba(0, 0, 0, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.1)", "borderRadius": "16px", "boxShadow": "0 8px 32px rgba(0, 0, 0, 0.3)"}, "glassmorphismAccent": {"background": "rgba(147, 51, 234, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(147, 51, 234, 0.2)", "borderRadius": "16px", "boxShadow": "0 8px 32px rgba(147, 51, 234, 0.1)"}}, "colorPalette": {"primary": {"50": "rgba(147, 51, 234, 0.05)", "100": "rgba(147, 51, 234, 0.1)", "200": "rgba(147, 51, 234, 0.2)", "300": "rgba(147, 51, 234, 0.3)", "500": "rgba(147, 51, 234, 0.5)", "700": "rgba(147, 51, 234, 0.7)", "900": "rgba(147, 51, 234, 0.9)"}, "secondary": {"50": "rgba(59, 130, 246, 0.05)", "100": "rgba(59, 130, 246, 0.1)", "200": "rgba(59, 130, 246, 0.2)", "300": "rgba(59, 130, 246, 0.3)", "500": "rgba(59, 130, 246, 0.5)", "700": "rgba(59, 130, 246, 0.7)", "900": "rgba(59, 130, 246, 0.9)"}, "success": {"100": "rgba(34, 197, 94, 0.1)", "200": "rgba(34, 197, 94, 0.2)", "500": "rgba(34, 197, 94, 0.5)"}, "warning": {"100": "rgba(251, 191, 36, 0.1)", "200": "rgba(251, 191, 36, 0.2)", "500": "rgba(251, 191, 36, 0.5)"}, "error": {"100": "rgba(239, 68, 68, 0.1)", "200": "rgba(239, 68, 68, 0.2)", "500": "rgba(239, 68, 68, 0.5)"}, "glass": {"light": "rgba(255, 255, 255, 0.1)", "medium": "rgba(255, 255, 255, 0.2)", "dark": "rgba(0, 0, 0, 0.1)", "border": "rgba(255, 255, 255, 0.2)"}}, "typography": {"fontFamily": "'Inter', 'Segoe UI', sans-serif", "sizes": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem", "2xl": "1.5rem", "3xl": "1.875rem", "4xl": "2.25rem", "5xl": "3rem"}, "weights": {"light": "300", "normal": "400", "medium": "500", "semibold": "600", "bold": "700"}}, "spacing": {"xs": "0.25rem", "sm": "0.5rem", "md": "1rem", "lg": "1.5rem", "xl": "2rem", "2xl": "3rem", "3xl": "4rem"}, "borderRadius": {"sm": "8px", "md": "12px", "lg": "16px", "xl": "20px", "2xl": "24px", "full": "9999px"}, "shadows": {"glass": "0 8px 32px rgba(0, 0, 0, 0.1)", "glassHover": "0 12px 40px rgba(0, 0, 0, 0.15)", "glassActive": "0 4px 16px rgba(0, 0, 0, 0.2)", "inner": "inset 0 1px 2px rgba(0, 0, 0, 0.1)"}, "blur": {"sm": "blur(4px)", "md": "blur(8px)", "lg": "blur(12px)", "xl": "blur(16px)", "2xl": "blur(24px)"}, "animations": {"fadeIn": "fadeIn 0.3s ease-in-out", "slideUp": "slideUp 0.4s ease-out", "scaleIn": "scaleIn 0.2s ease-out", "shimmer": "shimmer 2s infinite"}}, "components": {"Button": {"variants": {"primary": {"base": {"background": "rgba(147, 51, 234, 0.2)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(147, 51, 234, 0.3)", "borderRadius": "12px", "padding": "0.75rem 1.5rem", "color": "white", "fontWeight": "500", "fontSize": "1rem", "boxShadow": "0 8px 32px rgba(147, 51, 234, 0.1)", "cursor": "pointer", "transition": "all 0.3s ease"}, "hover": {"background": "rgba(147, 51, 234, 0.3)", "boxShadow": "0 12px 40px rgba(147, 51, 234, 0.2)", "transform": "translateY(-2px)"}, "active": {"background": "rgba(147, 51, 234, 0.4)", "transform": "translateY(0px)"}, "disabled": {"opacity": "0.5", "cursor": "not-allowed"}}, "secondary": {"base": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "12px", "padding": "0.75rem 1.5rem", "color": "white", "fontWeight": "500", "fontSize": "1rem", "boxShadow": "0 8px 32px rgba(0, 0, 0, 0.1)", "cursor": "pointer", "transition": "all 0.3s ease"}, "hover": {"background": "rgba(255, 255, 255, 0.2)", "boxShadow": "0 12px 40px rgba(0, 0, 0, 0.15)"}}, "ghost": {"base": {"background": "transparent", "border": "1px solid rgba(255, 255, 255, 0.1)", "borderRadius": "12px", "padding": "0.75rem 1.5rem", "color": "rgba(255, 255, 255, 0.8)", "fontWeight": "500", "fontSize": "1rem", "cursor": "pointer", "transition": "all 0.3s ease"}, "hover": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)"}}}, "sizes": {"sm": {"padding": "0.5rem 1rem", "fontSize": "0.875rem"}, "md": {"padding": "0.75rem 1.5rem", "fontSize": "1rem"}, "lg": {"padding": "1rem 2rem", "fontSize": "1.125rem"}}}, "Card": {"variants": {"default": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "16px", "padding": "1.5rem", "boxShadow": "0 8px 32px rgba(0, 0, 0, 0.1)", "transition": "all 0.3s ease"}, "hover": {"background": "rgba(255, 255, 255, 0.15)", "boxShadow": "0 12px 40px rgba(0, 0, 0, 0.15)", "transform": "translateY(-4px)"}, "interactive": {"cursor": "pointer"}}, "sizes": {"sm": {"padding": "1rem", "borderRadius": "12px"}, "md": {"padding": "1.5rem", "borderRadius": "16px"}, "lg": {"padding": "2rem", "borderRadius": "20px"}}}, "Input": {"variants": {"default": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "12px", "padding": "0.75rem 1rem", "color": "white", "fontSize": "1rem", "boxShadow": "0 8px 32px rgba(0, 0, 0, 0.1)", "transition": "all 0.3s ease", "outline": "none", "placeholder": "rgba(255, 255, 255, 0.6)"}, "focus": {"border": "1px solid rgba(147, 51, 234, 0.5)", "boxShadow": "0 0 0 3px rgba(147, 51, 234, 0.1)"}, "error": {"border": "1px solid rgba(239, 68, 68, 0.5)", "boxShadow": "0 0 0 3px rgba(239, 68, 68, 0.1)"}}}, "Modal": {"overlay": {"background": "rgba(0, 0, 0, 0.3)", "backdropFilter": "blur(4px)", "position": "fixed", "top": "0", "left": "0", "right": "0", "bottom": "0", "zIndex": "1000", "display": "flex", "alignItems": "center", "justifyContent": "center"}, "content": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(20px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "20px", "padding": "2rem", "maxWidth": "500px", "width": "90%", "boxShadow": "0 20px 60px rgba(0, 0, 0, 0.3)", "animation": "scaleIn 0.3s ease-out"}}, "Navigation": {"navbar": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "16px", "padding": "1rem 2rem", "boxShadow": "0 8px 32px rgba(0, 0, 0, 0.1)", "position": "sticky", "top": "1rem", "zIndex": "100"}, "navItem": {"base": {"padding": "0.5rem 1rem", "borderRadius": "8px", "color": "rgba(255, 255, 255, 0.8)", "textDecoration": "none", "transition": "all 0.3s ease", "fontWeight": "500"}, "hover": {"background": "rgba(255, 255, 255, 0.1)", "color": "white"}, "active": {"background": "rgba(147, 51, 234, 0.2)", "color": "white"}}}, "Dropdown": {"trigger": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "12px", "padding": "0.75rem 1rem", "color": "white", "cursor": "pointer", "transition": "all 0.3s ease"}, "content": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(15px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "12px", "padding": "0.5rem", "marginTop": "0.5rem", "boxShadow": "0 12px 40px rgba(0, 0, 0, 0.2)", "animation": "slideUp 0.2s ease-out"}, "item": {"base": {"padding": "0.75rem 1rem", "borderRadius": "8px", "color": "rgba(255, 255, 255, 0.8)", "cursor": "pointer", "transition": "all 0.2s ease"}, "hover": {"background": "rgba(255, 255, 255, 0.1)", "color": "white"}}}, "Tooltip": {"content": {"background": "rgba(0, 0, 0, 0.8)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.1)", "borderRadius": "8px", "padding": "0.5rem 0.75rem", "fontSize": "0.875rem", "color": "white", "boxShadow": "0 8px 32px rgba(0, 0, 0, 0.3)", "animation": "fadeIn 0.2s ease-out"}}, "Badge": {"variants": {"default": {"background": "rgba(255, 255, 255, 0.2)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.3)", "borderRadius": "20px", "padding": "0.25rem 0.75rem", "fontSize": "0.875rem", "color": "white", "fontWeight": "500"}, "success": {"background": "rgba(34, 197, 94, 0.2)", "border": "1px solid rgba(34, 197, 94, 0.3)", "color": "rgb(34, 197, 94)"}, "warning": {"background": "rgba(251, 191, 36, 0.2)", "border": "1px solid rgba(251, 191, 36, 0.3)", "color": "rgb(251, 191, 36)"}, "error": {"background": "rgba(239, 68, 68, 0.2)", "border": "1px solid rgba(239, 68, 68, 0.3)", "color": "rgb(239, 68, 68)"}}}, "Table": {"container": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "16px", "overflow": "hidden", "boxShadow": "0 8px 32px rgba(0, 0, 0, 0.1)"}, "header": {"background": "rgba(255, 255, 255, 0.1)", "padding": "1rem", "borderBottom": "1px solid rgba(255, 255, 255, 0.2)", "fontWeight": "600", "color": "white"}, "row": {"base": {"padding": "1rem", "borderBottom": "1px solid rgba(255, 255, 255, 0.1)", "color": "rgba(255, 255, 255, 0.9)", "transition": "all 0.2s ease"}, "hover": {"background": "rgba(255, 255, 255, 0.05)"}}}, "Tabs": {"container": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "16px", "padding": "0.5rem", "display": "flex", "gap": "0.5rem"}, "tab": {"base": {"padding": "0.75rem 1.5rem", "borderRadius": "12px", "color": "rgba(255, 255, 255, 0.7)", "cursor": "pointer", "transition": "all 0.3s ease", "fontWeight": "500"}, "active": {"background": "rgba(147, 51, 234, 0.3)", "color": "white", "boxShadow": "0 4px 16px rgba(147, 51, 234, 0.2)"}, "hover": {"background": "rgba(255, 255, 255, 0.1)", "color": "white"}}}, "ProgressBar": {"container": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "20px", "height": "8px", "overflow": "hidden"}, "fill": {"background": "linear-gradient(90deg, rgba(147, 51, 234, 0.8), rgba(59, 130, 246, 0.8))", "height": "100%", "borderRadius": "20px", "transition": "width 0.3s ease"}}, "Switch": {"track": {"base": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "20px", "width": "48px", "height": "24px", "cursor": "pointer", "transition": "all 0.3s ease"}, "active": {"background": "rgba(147, 51, 234, 0.3)", "border": "1px solid rgba(147, 51, 234, 0.5)"}}, "thumb": {"base": {"background": "rgba(255, 255, 255, 0.9)", "borderRadius": "50%", "width": "18px", "height": "18px", "boxShadow": "0 2px 8px rgba(0, 0, 0, 0.2)", "transition": "all 0.3s ease"}, "active": {"background": "white"}}}, "Sidebar": {"container": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(15px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "20px", "padding": "1.5rem", "width": "280px", "height": "100vh", "boxShadow": "0 8px 32px rgba(0, 0, 0, 0.1)"}, "item": {"base": {"padding": "0.75rem 1rem", "borderRadius": "12px", "color": "rgba(255, 255, 255, 0.8)", "cursor": "pointer", "transition": "all 0.3s ease", "display": "flex", "alignItems": "center", "gap": "0.75rem"}, "hover": {"background": "rgba(255, 255, 255, 0.1)", "color": "white"}, "active": {"background": "rgba(147, 51, 234, 0.2)", "color": "white"}}}, "Alert": {"variants": {"info": {"background": "rgba(59, 130, 246, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(59, 130, 246, 0.3)", "borderRadius": "12px", "padding": "1rem", "color": "rgb(59, 130, 246)"}, "success": {"background": "rgba(34, 197, 94, 0.1)", "border": "1px solid rgba(34, 197, 94, 0.3)", "color": "rgb(34, 197, 94)"}, "warning": {"background": "rgba(251, 191, 36, 0.1)", "border": "1px solid rgba(251, 191, 36, 0.3)", "color": "rgb(251, 191, 36)"}, "error": {"background": "rgba(239, 68, 68, 0.1)", "border": "1px solid rgba(239, 68, 68, 0.3)", "color": "rgb(239, 68, 68)"}}}, "Checkbox": {"base": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "4px", "width": "20px", "height": "20px", "cursor": "pointer", "transition": "all 0.3s ease"}, "checked": {"background": "rgba(147, 51, 234, 0.3)", "border": "1px solid rgba(147, 51, 234, 0.5)"}}, "RadioButton": {"base": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "50%", "width": "20px", "height": "20px", "cursor": "pointer", "transition": "all 0.3s ease"}, "checked": {"background": "rgba(147, 51, 234, 0.3)", "border": "1px solid rgba(147, 51, 234, 0.5)"}}, "Slider": {"track": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "20px", "height": "6px"}, "fill": {"background": "linear-gradient(90deg, rgba(147, 51, 234, 0.8), rgba(59, 130, 246, 0.8))", "height": "100%", "borderRadius": "20px"}, "thumb": {"background": "rgba(255, 255, 255, 0.9)", "border": "2px solid rgba(147, 51, 234, 0.5)", "borderRadius": "50%", "width": "20px", "height": "20px", "boxShadow": "0 2px 8px rgba(0, 0, 0, 0.2)", "cursor": "pointer"}}, "Pagination": {"container": {"display": "flex", "gap": "0.5rem", "alignItems": "center"}, "button": {"base": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "8px", "padding": "0.5rem 0.75rem", "color": "rgba(255, 255, 255, 0.8)", "cursor": "pointer", "transition": "all 0.3s ease"}, "hover": {"background": "rgba(255, 255, 255, 0.2)", "color": "white"}, "active": {"background": "rgba(147, 51, 234, 0.3)", "color": "white"}}}, "Breadcrumb": {"container": {"display": "flex", "alignItems": "center", "gap": "0.5rem"}, "item": {"color": "rgba(255, 255, 255, 0.8)", "textDecoration": "none", "fontSize": "0.875rem", "transition": "all 0.3s ease"}, "itemHover": {"color": "white"}, "separator": {"color": "rgba(255, 255, 255, 0.5)"}}, "Accordion": {"container": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "16px", "overflow": "hidden", "boxShadow": "0 8px 32px rgba(0, 0, 0, 0.1)"}, "header": {"base": {"padding": "1rem 1.5rem", "borderBottom": "1px solid rgba(255, 255, 255, 0.1)", "color": "white", "cursor": "pointer", "transition": "all 0.3s ease", "fontWeight": "500"}, "hover": {"background": "rgba(255, 255, 255, 0.05)"}}, "content": {"padding": "1rem 1.5rem", "color": "rgba(255, 255, 255, 0.8)", "animation": "slideUp 0.3s ease-out"}}, "DatePicker": {"container": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(15px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "16px", "padding": "1rem", "boxShadow": "0 12px 40px rgba(0, 0, 0, 0.2)", "animation": "scaleIn 0.3s ease-out"}, "input": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "12px", "padding": "0.75rem 1rem", "color": "white", "fontSize": "1rem", "cursor": "pointer"}, "calendar": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(15px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "12px", "padding": "1rem", "marginTop": "0.5rem"}, "day": {"base": {"padding": "0.5rem", "borderRadius": "8px", "color": "rgba(255, 255, 255, 0.8)", "cursor": "pointer", "transition": "all 0.2s ease"}, "hover": {"background": "rgba(255, 255, 255, 0.1)", "color": "white"}, "selected": {"background": "rgba(147, 51, 234, 0.3)", "color": "white"}}}, "TimePicker": {"container": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(15px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "12px", "padding": "1rem", "display": "flex", "gap": "1rem", "alignItems": "center"}, "input": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "8px", "padding": "0.5rem", "color": "white", "textAlign": "center", "width": "60px"}}, "ColorPicker": {"container": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(15px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "16px", "padding": "1rem", "boxShadow": "0 12px 40px rgba(0, 0, 0, 0.2)"}, "swatch": {"borderRadius": "8px", "width": "40px", "height": "40px", "border": "2px solid rgba(255, 255, 255, 0.2)", "cursor": "pointer", "transition": "all 0.3s ease"}, "swatchHover": {"border": "2px solid rgba(255, 255, 255, 0.5)", "transform": "scale(1.1)"}}, "FileUpload": {"dropzone": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "2px dashed rgba(255, 255, 255, 0.3)", "borderRadius": "16px", "padding": "3rem 2rem", "textAlign": "center", "color": "rgba(255, 255, 255, 0.8)", "cursor": "pointer", "transition": "all 0.3s ease"}, "dropzoneActive": {"border": "2px dashed rgba(147, 51, 234, 0.5)", "background": "rgba(147, 51, 234, 0.1)"}, "uploadButton": {"background": "rgba(147, 51, 234, 0.2)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(147, 51, 234, 0.3)", "borderRadius": "12px", "padding": "0.75rem 1.5rem", "color": "white", "cursor": "pointer", "marginTop": "1rem"}}, "Avatar": {"container": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "2px solid rgba(255, 255, 255, 0.2)", "borderRadius": "50%", "overflow": "hidden", "boxShadow": "0 8px 32px rgba(0, 0, 0, 0.1)"}, "sizes": {"xs": {"width": "24px", "height": "24px"}, "sm": {"width": "32px", "height": "32px"}, "md": {"width": "48px", "height": "48px"}, "lg": {"width": "64px", "height": "64px"}, "xl": {"width": "96px", "height": "96px"}}, "placeholder": {"background": "rgba(147, 51, 234, 0.3)", "color": "white", "display": "flex", "alignItems": "center", "justifyContent": "center", "fontSize": "1rem", "fontWeight": "500"}}, "Skeleton": {"base": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "borderRadius": "8px", "animation": "shimmer 2s infinite", "overflow": "hidden"}, "shimmer": {"background": "linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent)", "animation": "shimmer 2s infinite"}}, "Divider": {"horizontal": {"background": "rgba(255, 255, 255, 0.1)", "height": "1px", "width": "100%", "margin": "1rem 0"}, "vertical": {"background": "rgba(255, 255, 255, 0.1)", "width": "1px", "height": "100%", "margin": "0 1rem"}}, "Spinner": {"container": {"display": "inline-block", "animation": "spin 1s linear infinite"}, "circle": {"border": "2px solid rgba(255, 255, 255, 0.1)", "borderTop": "2px solid rgba(147, 51, 234, 0.8)", "borderRadius": "50%", "width": "24px", "height": "24px"}, "sizes": {"sm": {"width": "16px", "height": "16px"}, "md": {"width": "24px", "height": "24px"}, "lg": {"width": "32px", "height": "32px"}}}, "Toast": {"container": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(15px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "12px", "padding": "1rem 1.5rem", "boxShadow": "0 12px 40px rgba(0, 0, 0, 0.2)", "animation": "slideUp 0.3s ease-out", "minWidth": "300px"}, "variants": {"success": {"border": "1px solid rgba(34, 197, 94, 0.3)", "background": "rgba(34, 197, 94, 0.1)"}, "error": {"border": "1px solid rgba(239, 68, 68, 0.3)", "background": "rgba(239, 68, 68, 0.1)"}, "warning": {"border": "1px solid rgba(251, 191, 36, 0.3)", "background": "rgba(251, 191, 36, 0.1)"}, "info": {"border": "1px solid rgba(59, 130, 246, 0.3)", "background": "rgba(59, 130, 246, 0.1)"}}}, "ContextMenu": {"container": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(15px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "12px", "padding": "0.5rem", "boxShadow": "0 12px 40px rgba(0, 0, 0, 0.2)", "animation": "scaleIn 0.2s ease-out", "minWidth": "200px"}, "item": {"base": {"padding": "0.75rem 1rem", "borderRadius": "8px", "color": "rgba(255, 255, 255, 0.8)", "cursor": "pointer", "transition": "all 0.2s ease", "display": "flex", "alignItems": "center", "gap": "0.5rem"}, "hover": {"background": "rgba(255, 255, 255, 0.1)", "color": "white"}, "disabled": {"opacity": "0.5", "cursor": "not-allowed"}}, "separator": {"background": "rgba(255, 255, 255, 0.1)", "height": "1px", "margin": "0.5rem 0"}}, "Stepper": {"container": {"display": "flex", "alignItems": "center", "gap": "1rem"}, "step": {"base": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "2px solid rgba(255, 255, 255, 0.2)", "borderRadius": "50%", "width": "40px", "height": "40px", "display": "flex", "alignItems": "center", "justifyContent": "center", "color": "rgba(255, 255, 255, 0.8)", "fontWeight": "500", "transition": "all 0.3s ease"}, "active": {"background": "rgba(147, 51, 234, 0.3)", "border": "2px solid rgba(147, 51, 234, 0.5)", "color": "white"}, "completed": {"background": "rgba(34, 197, 94, 0.3)", "border": "2px solid rgba(34, 197, 94, 0.5)", "color": "white"}}, "connector": {"background": "rgba(255, 255, 255, 0.1)", "height": "2px", "flex": "1", "transition": "all 0.3s ease"}, "connectorActive": {"background": "rgba(147, 51, 234, 0.5)"}}, "Rating": {"container": {"display": "flex", "gap": "0.25rem"}, "star": {"base": {"color": "rgba(255, 255, 255, 0.3)", "fontSize": "1.5rem", "cursor": "pointer", "transition": "all 0.2s ease"}, "active": {"color": "rgba(251, 191, 36, 0.8)"}, "hover": {"color": "rgba(251, 191, 36, 1)", "transform": "scale(1.1)"}}}, "Timeline": {"container": {"position": "relative", "paddingLeft": "2rem"}, "line": {"position": "absolute", "left": "1rem", "top": "0", "bottom": "0", "width": "2px", "background": "rgba(255, 255, 255, 0.2)"}, "item": {"position": "relative", "marginBottom": "2rem"}, "marker": {"position": "absolute", "left": "-2rem", "top": "0.5rem", "width": "12px", "height": "12px", "background": "rgba(147, 51, 234, 0.8)", "borderRadius": "50%", "border": "2px solid rgba(255, 255, 255, 0.2)"}, "content": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "12px", "padding": "1rem", "color": "rgba(255, 255, 255, 0.9)"}}, "Calendar": {"container": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(15px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "16px", "padding": "1.5rem", "boxShadow": "0 12px 40px rgba(0, 0, 0, 0.2)"}, "header": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "1rem", "color": "white", "fontWeight": "600"}, "grid": {"display": "grid", "gridTemplateColumns": "repeat(7, 1fr)", "gap": "0.25rem"}, "dayHeader": {"padding": "0.5rem", "textAlign": "center", "color": "rgba(255, 255, 255, 0.6)", "fontSize": "0.875rem", "fontWeight": "500"}, "day": {"base": {"padding": "0.75rem", "textAlign": "center", "borderRadius": "8px", "color": "rgba(255, 255, 255, 0.8)", "cursor": "pointer", "transition": "all 0.2s ease"}, "hover": {"background": "rgba(255, 255, 255, 0.1)", "color": "white"}, "selected": {"background": "rgba(147, 51, 234, 0.3)", "color": "white"}, "today": {"background": "rgba(59, 130, 246, 0.2)", "color": "white"}}}}, "layouts": {"Container": {"maxWidth": "1200px", "margin": "0 auto", "padding": "0 1rem"}, "Grid": {"display": "grid", "gap": "1rem", "variants": {"cols1": {"gridTemplateColumns": "1fr"}, "cols2": {"gridTemplateColumns": "repeat(2, 1fr)"}, "cols3": {"gridTemplateColumns": "repeat(3, 1fr)"}, "cols4": {"gridTemplateColumns": "repeat(4, 1fr)"}, "cols12": {"gridTemplateColumns": "repeat(12, 1fr)"}}}, "Flexbox": {"display": "flex", "variants": {"row": {"flexDirection": "row"}, "column": {"flexDirection": "column"}, "center": {"alignItems": "center", "justifyContent": "center"}, "spaceBetween": {"justifyContent": "space-between"}, "spaceAround": {"justifyContent": "space-around"}}}, "Stack": {"display": "flex", "flexDirection": "column", "gap": "1rem"}}, "patterns": {"Hero": {"container": {"background": "rgba(255, 255, 255, 0.05)", "backdropFilter": "blur(20px)", "border": "1px solid rgba(255, 255, 255, 0.1)", "borderRadius": "24px", "padding": "4rem 2rem", "textAlign": "center", "boxShadow": "0 20px 60px rgba(0, 0, 0, 0.2)"}, "title": {"fontSize": "3rem", "fontWeight": "700", "color": "white", "marginBottom": "1rem", "background": "linear-gradient(135deg, rgba(147, 51, 234, 1), rgba(59, 130, 246, 1))", "WebkitBackgroundClip": "text", "WebkitTextFillColor": "transparent"}, "subtitle": {"fontSize": "1.25rem", "color": "rgba(255, 255, 255, 0.8)", "marginBottom": "2rem"}}, "Feature": {"container": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "16px", "padding": "2rem", "textAlign": "center", "boxShadow": "0 8px 32px rgba(0, 0, 0, 0.1)", "transition": "all 0.3s ease"}, "hover": {"background": "rgba(255, 255, 255, 0.15)", "transform": "translateY(-8px)", "boxShadow": "0 16px 48px rgba(0, 0, 0, 0.2)"}, "icon": {"background": "rgba(147, 51, 234, 0.2)", "borderRadius": "50%", "width": "64px", "height": "64px", "display": "flex", "alignItems": "center", "justifyContent": "center", "margin": "0 auto 1rem", "fontSize": "1.5rem", "color": "rgba(147, 51, 234, 1)"}}, "Pricing": {"container": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(15px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "20px", "padding": "2rem", "textAlign": "center", "boxShadow": "0 12px 40px rgba(0, 0, 0, 0.15)", "transition": "all 0.3s ease"}, "popular": {"border": "2px solid rgba(147, 51, 234, 0.5)", "background": "rgba(147, 51, 234, 0.1)", "transform": "scale(1.05)"}, "price": {"fontSize": "3rem", "fontWeight": "700", "color": "white", "marginBottom": "1rem"}, "features": {"listStyle": "none", "padding": "0", "margin": "1rem 0"}, "feature": {"padding": "0.5rem 0", "color": "rgba(255, 255, 255, 0.8)", "borderBottom": "1px solid rgba(255, 255, 255, 0.1)"}}, "Testimonial": {"container": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "16px", "padding": "2rem", "boxShadow": "0 8px 32px rgba(0, 0, 0, 0.1)"}, "quote": {"fontSize": "1.125rem", "color": "rgba(255, 255, 255, 0.9)", "fontStyle": "italic", "marginBottom": "1rem"}, "author": {"display": "flex", "alignItems": "center", "gap": "1rem"}, "authorName": {"color": "white", "fontWeight": "500"}, "authorTitle": {"color": "rgba(255, 255, 255, 0.6)", "fontSize": "0.875rem"}}, "ContactForm": {"container": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(15px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "20px", "padding": "2rem", "boxShadow": "0 12px 40px rgba(0, 0, 0, 0.2)"}, "fieldGroup": {"marginBottom": "1.5rem"}, "label": {"display": "block", "color": "rgba(255, 255, 255, 0.9)", "fontWeight": "500", "marginBottom": "0.5rem"}}}, "animations": {"keyframes": {"fadeIn": {"from": {"opacity": "0"}, "to": {"opacity": "1"}}, "slideUp": {"from": {"opacity": "0", "transform": "translateY(20px)"}, "to": {"opacity": "1", "transform": "translateY(0)"}}, "scaleIn": {"from": {"opacity": "0", "transform": "scale(0.9)"}, "to": {"opacity": "1", "transform": "scale(1)"}}, "shimmer": {"0%": {"transform": "translateX(-100%)"}, "100%": {"transform": "translateX(100%)"}}, "spin": {"from": {"transform": "rotate(0deg)"}, "to": {"transform": "rotate(360deg)"}}}}, "responsiveBreakpoints": {"sm": "640px", "md": "768px", "lg": "1024px", "xl": "1280px", "2xl": "1536px"}, "implementationGuide": {"cssVariables": {"description": "Define CSS custom properties for consistent theming", "example": {":root": {"--glass-bg": "rgba(255, 255, 255, 0.1)", "--glass-border": "rgba(255, 255, 255, 0.2)", "--glass-blur": "blur(10px)", "--glass-shadow": "0 8px 32px rgba(0, 0, 0, 0.1)"}}}, "browserSupport": {"backdrop-filter": "Use -webkit-backdrop-filter for Safari support", "fallback": "Provide solid color fallbacks for older browsers"}, "performance": {"optimization": ["Use transform and opacity for animations", "Minimize backdrop-filter usage on frequently changing elements", "Use will-change property for animated elements", "Optimize blur radius for performance"]}, "accessibility": {"contrast": "Ensure sufficient contrast ratios with transparent backgrounds", "focusStates": "Provide clear focus indicators with glass effects", "reducedMotion": "Respect prefers-reduced-motion media query"}, "bestPractices": {"layering": "Use multiple glass layers for depth", "consistency": "Maintain consistent blur and opacity values", "contextualUsage": "Use darker glass on light backgrounds and lighter glass on dark backgrounds", "gradientBackgrounds": "Glassmorphism works best with gradient or image backgrounds"}}}