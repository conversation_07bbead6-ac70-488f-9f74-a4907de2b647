#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单API测试脚本
Order API Test Script
"""

import sys
import os
import json
from datetime import datetime, date

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from api.order_api import OrderAPI
    from config.settings import settings
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

def test_order_api():
    """测试订单API功能"""
    print("=" * 60)
    print("🧪 订单API功能测试")
    print("=" * 60)
    
    # 创建API实例
    api_url = "https://st.pcylsoft.com:9006/st/steelyard/"
    api = OrderAPI(api_url)
    print(f"API地址: {api_url}")
    
    # 注意：这里需要真实的token才能测试
    # 在实际使用中，token应该通过登录获取
    test_token = "your_test_token_here"
    
    print("\n⚠️ 注意: 需要有效的access_token才能进行实际测试")
    print("请先通过登录接口获取token，然后替换test_token变量")
    
    # 如果有测试token，可以设置
    if test_token != "your_test_token_here":
        api.set_access_token(test_token)
        print(f"✅ 已设置测试Token: {test_token[:20]}...")
        
        # 测试获取订单
        test_get_orders(api)
        test_get_today_orders(api)
        test_search_functions(api)
        test_statistics(api)
    else:
        print("❌ 未设置有效token，跳过API测试")
        
        # 演示API使用方法
        demo_api_usage()

def test_get_orders(api: OrderAPI):
    """测试获取订单功能"""
    print("\n" + "-" * 40)
    print("📋 测试获取订单功能")
    print("-" * 40)
    
    try:
        # 测试获取所有订单
        print("1. 获取所有订单...")
        result = api.get_orders()
        print(f"响应状态: {result.get('code')}")
        print(f"响应消息: {result.get('msg')}")
        
        if result.get('code') == 200:
            data = result.get('data', [])
            total = result.get('total', 0)
            print(f"✅ 成功获取 {total} 个订单")
            
            if data:
                print(f"第一个订单示例:")
                first_order = data[0]
                print(f"  订单号: {first_order.get('trade_no')}")
                print(f"  供应商: {first_order.get('circulate_name')}")
                print(f"  下单时间: {first_order.get('collect_time')}")
                print(f"  商品种类: {first_order.get('item_count')}")
        else:
            print(f"❌ 获取订单失败: {result.get('msg')}")
            
        # 测试按日期获取订单
        print("\n2. 按日期获取订单...")
        test_date = "2023-06-08"
        result = api.get_orders(test_date)
        print(f"日期 {test_date} 的订单数量: {result.get('total', 0)}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

def test_get_today_orders(api: OrderAPI):
    """测试获取今日订单"""
    print("\n" + "-" * 40)
    print("📅 测试获取今日订单")
    print("-" * 40)
    
    try:
        result = api.get_today_orders()
        print(f"今日订单数量: {result.get('total', 0)}")
        
        if result.get('code') == 200:
            print("✅ 今日订单获取成功")
        else:
            print(f"❌ 获取今日订单失败: {result.get('msg')}")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

def test_search_functions(api: OrderAPI):
    """测试搜索功能"""
    print("\n" + "-" * 40)
    print("🔍 测试搜索功能")
    print("-" * 40)
    
    try:
        # 测试按订单号搜索
        print("1. 按订单号搜索...")
        test_trade_no = "CD22030712669"
        order = api.search_orders_by_trade_no(test_trade_no)
        
        if order:
            print(f"✅ 找到订单: {order.get('trade_no')}")
            print(f"  供应商: {order.get('circulate_name')}")
        else:
            print(f"❌ 未找到订单号为 {test_trade_no} 的订单")
        
        # 测试按供应商搜索
        print("\n2. 按供应商搜索...")
        test_supplier = "测试供应商"
        orders = api.search_orders_by_supplier(test_supplier)
        print(f"包含 '{test_supplier}' 的订单数量: {len(orders)}")
        
        if orders:
            print("✅ 搜索到相关订单:")
            for order in orders[:3]:  # 只显示前3个
                print(f"  - {order.get('trade_no')}: {order.get('circulate_name')}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

def test_statistics(api: OrderAPI):
    """测试统计功能"""
    print("\n" + "-" * 40)
    print("📊 测试统计功能")
    print("-" * 40)
    
    try:
        stats = api.get_order_statistics()
        
        print("订单统计信息:")
        print(f"  总订单数: {stats.get('total_orders', 0)}")
        print(f"  商品种类总数: {stats.get('total_items', 0)}")
        print(f"  采购总数量: {stats.get('total_buy_quantity', 0)}")
        print(f"  配送总数量: {stats.get('total_deliver_quantity', 0)}")
        print(f"  供应商数量: {len(stats.get('suppliers', []))}")
        
        suppliers = stats.get('suppliers', [])
        if suppliers:
            print("  供应商列表:")
            for supplier in suppliers[:5]:  # 只显示前5个
                print(f"    - {supplier}")
        
        order_states = stats.get('order_states', {})
        if order_states:
            print("  订单状态分布:")
            state_names = {0: "待处理", 1: "处理中", 2: "已完成", 3: "已取消"}
            for state, count in order_states.items():
                state_name = state_names.get(state, f"状态{state}")
                print(f"    - {state_name}: {count}")
        
        print("✅ 统计功能测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

def demo_api_usage():
    """演示API使用方法"""
    print("\n" + "=" * 60)
    print("📖 订单API使用方法演示")
    print("=" * 60)
    
    print("""
🔧 基本使用步骤:

1. 创建API实例:
   from api.order_api import OrderAPI
   api = OrderAPI("https://st.pcylsoft.com:9006/st/steelyard/")

2. 设置访问令牌:
   api.set_access_token(your_access_token)

3. 获取订单:
   # 获取所有订单
   result = api.get_orders()
   
   # 获取指定日期的订单
   result = api.get_orders("2023-06-08")
   
   # 获取今日订单
   result = api.get_today_orders()

4. 搜索订单:
   # 按订单号搜索
   order = api.search_orders_by_trade_no("CD22030712669")
   
   # 按供应商搜索
   orders = api.search_orders_by_supplier("测试供应商")

5. 获取统计信息:
   stats = api.get_order_statistics()

📋 响应数据格式:
{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "trade_no": "订单号",
            "circulate_name": "供应商名称",
            "collect_time": "下单时间",
            "state": 0,  // 订单状态
            "item_count": 2,  // 商品种类数
            "buy_quantity": "采购数量",
            "deliver_quantity": "配送数量",
            "goods": [  // 商品详情
                {
                    "name": "商品名称",
                    "code": "商品编码",
                    "buy_quantity": "采购数量",
                    "deliver_quantity": "配送数量",
                    ...
                }
            ]
        }
    ],
    "total": 6
}

🎯 状态说明:
- 0: 待处理
- 1: 处理中
- 2: 已完成
- 3: 已取消
""")

def test_ui_integration():
    """测试UI集成"""
    print("\n" + "=" * 60)
    print("🎨 UI集成测试")
    print("=" * 60)
    
    try:
        from PyQt6.QtWidgets import QApplication
        try:
            from ui.modules.order_module import OrderModule
            from api.order_api import OrderAPI
        except ImportError as e:
            print(f"❌ 导入UI模块失败: {e}")
            return False
        
        print("✅ 成功导入UI模块")
        
        # 创建应用（如果还没有的话）
        if not QApplication.instance():
            app = QApplication(sys.argv)
        else:
            app = QApplication.instance()
        
        print("✅ QApplication 创建成功")
        
        # 创建订单模块
        order_module = OrderModule()
        print("✅ 订单模块创建成功")
        
        # 创建API实例
        api = OrderAPI("https://st.pcylsoft.com:9006/st/steelyard/")
        order_module.set_api(api)
        print("✅ API实例设置成功")
        
        print("\n💡 UI模块功能说明:")
        print("  • 日期选择器：选择特定日期的订单")
        print("  • 供应商搜索：按供应商名称筛选")
        print("  • 订单号搜索：按订单号筛选")
        print("  • 订单列表：显示订单基本信息")
        print("  • 订单详情：显示选中订单的详细信息")
        print("  • 实时筛选：支持多条件组合筛选")
        
        return True
        
    except Exception as e:
        print(f"❌ UI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始订单功能测试...")
    
    # 测试API功能
    test_order_api()
    
    # 测试UI集成
    test_ui_integration()
    
    print("\n" + "=" * 60)
    print("✅ 订单功能测试完成！")
    print("=" * 60)
    
    print("""
🎉 订单管理功能已就绪！

📋 主要功能:
  ✅ 获取订单列表（支持按日期筛选）
  ✅ 订单搜索（按订单号、供应商）
  ✅ 订单详情展示
  ✅ 订单统计分析
  ✅ 图形化界面操作

🔧 使用方法:
  1. 启动主程序: python run.py
  2. 登录系统获取token
  3. 点击"订单管理"进入订单页面
  4. 使用各种筛选和搜索功能

⚠️ 注意事项:
  • 需要有效的网络连接
  • 需要正确的API地址和token
  • 建议先测试API连接
""")

if __name__ == "__main__":
    main()
