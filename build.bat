@echo off
chcp 65001 >nul
echo ========================================
echo 智慧食堂管理系统 - 打包脚本
echo ========================================
echo.

echo [1/4] 清理之前的构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "*.spec" del /q "*.spec" 2>nul
echo 清理完成。

echo.
echo [2/4] 开始打包程序...
echo 这可能需要几分钟时间，请耐心等待...
echo.

REM 使用spec文件进行打包
pyinstaller MaiCui.spec

if %errorlevel% neq 0 (
    echo.
    echo ❌ 打包失败！请检查错误信息。
    pause
    exit /b 1
)

echo.
echo [3/4] 打包完成，正在整理文件...

REM 创建发布目录
if not exist "release" mkdir "release"

REM 复制可执行文件
if exist "dist\智慧食堂管理系统.exe" (
    copy "dist\智慧食堂管理系统.exe" "release\"
    echo ✅ 单文件版本已复制到 release 目录
)

REM 复制目录版本
if exist "dist\智慧食堂管理系统_目录版" (
    xcopy "dist\智慧食堂管理系统_目录版" "release\智慧食堂管理系统_目录版\" /e /i /h /y
    echo ✅ 目录版本已复制到 release 目录
)

REM 复制必要的配置文件到release目录
if exist "config" (
    xcopy "config" "release\config\" /e /i /h /y
    echo ✅ 配置文件已复制
)

REM 创建使用说明
echo 创建使用说明文件...
(
echo 智慧食堂管理系统 - 使用说明
echo ================================
echo.
echo 本软件包包含以下文件：
echo.
echo 1. 智慧食堂管理系统.exe - 单文件可执行版本（推荐）
echo    - 双击即可运行，无需安装
echo    - 包含所有依赖，文件较大但使用方便
echo.
echo 2. 智慧食堂管理系统_目录版\ - 目录版本
echo    - 运行其中的"智慧食堂管理系统.exe"
echo    - 文件分散，启动稍快，但需要保持目录完整
echo.
echo 3. config\ - 配置文件目录
echo    - 包含系统配置和API设置
echo    - 可根据需要修改配置
echo.
echo 系统要求：
echo - Windows 7 及以上版本
echo - 无需安装Python环境
echo - 建议内存：4GB以上
echo.
echo 首次运行：
echo 1. 双击"智慧食堂管理系统.exe"启动程序
echo 2. 在登录界面输入用户名和密码
echo 3. 如需修改API地址，请编辑config\app_config.json文件
echo.
echo 技术支持：
echo 如遇问题，请联系系统管理员
echo.
echo 版本：1.0.0
echo 构建时间：%date% %time%
) > "release\使用说明.txt"

echo.
echo [4/4] 打包完成！
echo.
echo ========================================
echo ✅ 打包成功完成！
echo ========================================
echo.
echo 📁 发布文件位置：release\ 目录
echo.
echo 📋 包含文件：
if exist "release\智慧食堂管理系统.exe" echo    ✅ 智慧食堂管理系统.exe （单文件版本）
if exist "release\智慧食堂管理系统_目录版" echo    ✅ 智慧食堂管理系统_目录版\ （目录版本）
if exist "release\config" echo    ✅ config\ （配置文件）
if exist "release\使用说明.txt" echo    ✅ 使用说明.txt
echo.
echo 💡 建议使用单文件版本（智慧食堂管理系统.exe）
echo    可直接复制到其他电脑运行，无需Python环境
echo.
pause
