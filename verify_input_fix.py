#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证输入框修复
Verify Input Fix
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    print("🔧 输入框显示修复验证")
    print("=" * 50)
    
    try:
        # 测试样式导入
        from ui.styles import GlassmorphismStyles
        print("✅ 样式模块导入成功")
        
        # 获取输入框样式
        input_style = GlassmorphismStyles.get_input_style()
        print("✅ 输入框样式获取成功")
        
        # 检查关键样式属性
        checks = [
            ("height: 48px", "固定高度"),
            ("padding: 12px 16px", "内边距"),
            ("border-radius: 16px", "圆角半径"),
            ("font-size: 16px", "字体大小"),
            ("color: white", "文字颜色")
        ]
        
        print("\n🔍 样式检查:")
        all_good = True
        for check, desc in checks:
            if check in input_style:
                print(f"  ✅ {desc}: {check}")
            else:
                print(f"  ❌ {desc}: 未找到 {check}")
                all_good = False
        
        if all_good:
            print("\n🎉 所有样式检查通过！")
        else:
            print("\n⚠️ 部分样式检查失败")
        
        # 测试登录窗口导入
        from ui.login_window import LoginWindow
        print("✅ 登录窗口导入成功")
        
        print("\n📋 修复总结:")
        print("  • 输入框高度: 48px (固定)")
        print("  • 内边距: 12px 16px (适中)")
        print("  • 圆角: 16px (统一)")
        print("  • 字体: 16px 白色")
        print("  • 移除了过大的高度限制")
        
        print("\n🚀 现在可以运行程序:")
        print("  python main.py")
        print("  python start_fixed.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
