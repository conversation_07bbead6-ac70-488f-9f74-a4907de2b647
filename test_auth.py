#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证功能测试脚本
Authentication Test Script
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_auth_manager():
    """测试认证管理器"""
    print("=" * 50)
    print("🔐 测试认证管理器")
    print("=" * 50)
    
    try:
        from utils.auth_manager import auth_manager
        
        print("✅ 认证管理器导入成功")
        
        # 测试保存登录信息
        print("\n📝 测试保存登录信息...")
        test_username = "test_user"
        test_token = "test_token_12345"
        
        auth_manager.save_login_success(test_username, test_token, expire_hours=1)
        print(f"保存用户: {test_username}")
        
        # 测试获取当前认证信息
        print("\n🔍 测试获取认证信息...")
        username, token = auth_manager.get_current_auth()
        print(f"当前用户: {username}")
        print(f"当前Token: {token}")
        print(f"Token有效性: {'有效' if auth_manager.is_token_valid() else '无效'}")
        
        # 测试过期信息
        expire_info = auth_manager.get_token_expire_info()
        if expire_info:
            print(f"过期信息: {expire_info}")
        
        # 测试历史记录
        print("\n📚 测试历史记录...")
        history = auth_manager.get_login_history()
        print(f"历史记录数量: {len(history)}")
        for i, item in enumerate(history[:3]):  # 只显示前3个
            username = item.get('username', '')
            login_count = item.get('login_count', 0)
            print(f"  {i+1}. {username} (登录{login_count}次)")
        
        # 测试添加更多历史记录
        print("\n➕ 添加更多测试用户...")
        test_users = ["admin", "manager", "operator"]
        for user in test_users:
            auth_manager.add_to_history(user)
        
        history = auth_manager.get_login_history()
        print(f"更新后历史记录数量: {len(history)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 认证管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_integration():
    """测试API集成"""
    print("\n" + "=" * 50)
    print("🌐 测试API集成")
    print("=" * 50)
    
    try:
        from api.auth_api import AuthAPI
        from config.settings import settings
        
        print("✅ API模块导入成功")
        
        # 创建API实例
        api = AuthAPI(settings.api_base_url)
        print(f"API地址: {settings.api_base_url}")
        
        # 测试token验证功能
        print("\n🔍 测试Token验证功能...")
        
        # 设置一个测试token
        test_token = "test_token_12345"
        api.set_access_token(test_token)
        
        print(f"设置测试Token: {test_token}")
        print(f"认证状态: {'已认证' if api.is_authenticated() else '未认证'}")
        
        # 注意：这里不会真正调用API，因为是测试token
        print("⚠️ 注意: 实际的token验证需要真实的API服务器")
        
        return True
        
    except Exception as e:
        print(f"❌ API集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_components():
    """测试UI组件"""
    print("\n" + "=" * 50)
    print("🎨 测试UI组件")
    print("=" * 50)
    
    try:
        from PyQt6.QtWidgets import QApplication
        
        # 创建应用（如果还没有的话）
        if not QApplication.instance():
            app = QApplication(sys.argv)
        else:
            app = QApplication.instance()
        
        print("✅ QApplication 创建成功")
        
        # 测试登录窗口导入
        from ui.login_window import LoginWindow, AccountSelectionDialog, TokenVerifyThread
        print("✅ 登录窗口组件导入成功")
        
        # 测试创建登录窗口
        login_window = LoginWindow()
        print("✅ 登录窗口创建成功")
        
        # 测试账号选择对话框
        test_history = [
            {'username': 'admin', 'last_login': time.time(), 'login_count': 5},
            {'username': 'user1', 'last_login': time.time() - 3600, 'login_count': 3}
        ]
        
        dialog = AccountSelectionDialog(None, test_history)
        print("✅ 账号选择对话框创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_storage_file():
    """测试存储文件"""
    print("\n" + "=" * 50)
    print("💾 测试存储文件")
    print("=" * 50)
    
    try:
        from utils.auth_manager import auth_manager
        
        # 检查存储文件
        storage_file = auth_manager.storage_file
        print(f"存储文件路径: {storage_file}")
        
        if os.path.exists(storage_file):
            print("✅ 存储文件存在")
            
            # 读取文件内容
            import json
            with open(storage_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print("📄 存储文件内容:")
            print(f"  当前认证: {'有' if data.get('current_auth') else '无'}")
            print(f"  历史记录: {len(data.get('login_history', []))}条")
            
        else:
            print("⚠️ 存储文件不存在，将在首次使用时创建")
        
        return True
        
    except Exception as e:
        print(f"❌ 存储文件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 智慧食堂管理系统 - 认证功能测试")
    print("=" * 60)
    
    tests = [
        ("认证管理器", test_auth_manager),
        ("API集成", test_api_integration),
        ("UI组件", test_ui_components),
        ("存储文件", test_storage_file)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试过程中发生错误: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！新的认证功能已就绪")
        print("\n💡 新功能说明:")
        print("  • Token过期检查和自动登录")
        print("  • 历史账号记忆和选择")
        print("  • 认证信息持久化存储")
        print("  • 启动时自动验证token")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
