智慧食堂管理系统 - 更新说明
================================

版本：v1.0.1 (修复版)
更新时间：2025年7月30日

🔧 本次更新内容：
================================

1. 修复登录API地址问题
   - 问题：登录时出现"Failed to parse: https://[ip:port]/st/steelyard/?op=login"错误
   - 原因：代码中存在占位符URL未被正确替换
   - 修复：将所有占位符URL替换为正确的API地址
   
   修复的文件：
   ✅ api/auth_api.py - 修复默认base_url
   ✅ config/settings.py - 修复默认配置中的base_url
   ✅ ui/main_window.py - 使用配置文件中的API地址

2. 确保配置文件正确加载
   - 验证app_config.json中的API地址配置
   - 确保程序优先使用配置文件中的设置
   - 提供合理的默认值作为备选

🎯 修复效果：
================================
- ✅ 登录功能现在应该能正常工作
- ✅ API请求使用正确的服务器地址
- ✅ 不再出现占位符URL错误
- ✅ 配置文件设置得到正确应用

📋 使用说明：
================================
1. 如果之前的版本无法登录，请使用此修复版本
2. 确保config/app_config.json中的API地址正确：
   "base_url": "https://st.pcylsoft.com:9006/st/steelyard/"
3. 如需修改API地址，请编辑配置文件后重启程序

🔍 技术细节：
================================
修复前的问题URL：
https://[ip:port]/st/steelyard/?op=login

修复后的正确URL：
https://st.pcylsoft.com:9006/st/steelyard/?op=login

代码修改点：
- AuthAPI类的默认构造参数
- Settings类的默认配置
- MainWindow中的API实例创建

⚠️ 重要提醒：
================================
- 请使用此修复版本替换之前的程序文件
- 保留config目录中的配置文件
- 如果仍有问题，请检查网络连接和API服务器状态

📞 技术支持：
================================
如果更新后仍有问题，请提供以下信息：
1. 错误信息截图
2. 网络连接状态
3. config/app_config.json文件内容

版本历史：
================================
v1.0.1 (2025-07-30) - 修复版
- 修复登录API地址占位符问题
- 确保配置文件正确加载
- 提高程序稳定性

v1.0.0 (2025-07-30) - 初始版本
- 基础功能实现
- 玻璃态UI设计
- 完整的API集成
