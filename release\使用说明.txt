智慧食堂管理系统 - 使用说明
================================

版本：1.0.0
构建时间：2025年7月30日
系统要求：Windows 7 及以上版本

本软件包包含以下文件：
================================

1. 智慧食堂管理系统.exe - 单文件可执行版本（推荐）
   - 文件大小：约 39MB
   - 双击即可运行，无需安装
   - 包含所有依赖，文件较大但使用方便
   - 适合快速部署和使用

2. 智慧食堂管理系统_目录版\ - 目录版本
   - 运行其中的"智慧食堂管理系统.exe"
   - 文件分散，启动稍快，但需要保持目录完整
   - 适合需要频繁使用的场景

3. config\ - 配置文件目录
   - app_config.json - 应用配置文件
   - GrilmorphismUI.json - UI设计规范
   - settings.py - 系统设置
   - 可根据需要修改配置

系统要求：
================================
- 操作系统：Windows 7 及以上版本
- 内存：建议 4GB 以上
- 硬盘空间：至少 100MB 可用空间
- 网络：需要网络连接以访问API服务
- 无需安装Python环境

首次运行：
================================
1. 双击"智慧食堂管理系统.exe"启动程序
2. 程序会显示登录界面
3. 输入用户名和密码进行登录
4. 登录成功后进入主界面

配置说明：
================================
如需修改API服务器地址，请编辑 config\app_config.json 文件：

{
  "api": {
    "base_url": "https://your-api-server.com/st/steelyard/"
  },
  "ui": {
    "window_width": 1200,
    "window_height": 800,
    "remember_username": true
  }
}

功能特色：
================================
✅ 现代化玻璃态UI设计
✅ 用户登录认证系统
✅ 食谱管理功能
✅ 响应式界面布局
✅ 完整的API集成
✅ 配置文件管理

主要功能模块：
================================
- 登录系统：安全的用户认证
- 主界面：功能导航和状态显示
- 食谱管理：创建、编辑、删除食谱
- 系统设置：配置管理和用户偏好

部署说明：
================================
1. 将整个 release 文件夹复制到目标电脑
2. 确保目标电脑满足系统要求
3. 双击运行"智慧食堂管理系统.exe"
4. 根据需要修改配置文件

故障排除：
================================
Q: 程序无法启动？
A: 检查系统是否为Windows 7及以上版本，确保有足够的内存空间

Q: 登录失败？
A: 检查网络连接，确认API服务器地址配置正确

Q: 界面显示异常？
A: 尝试调整系统显示缩放设置，建议使用100%缩放

Q: 程序运行缓慢？
A: 建议使用目录版本，或增加系统内存

技术支持：
================================
如遇到其他问题，请联系系统管理员或技术支持团队。

更新日志：
================================
v1.0.0 (2025-07-30)
- 初始版本发布
- 实现基础登录和食谱管理功能
- 采用现代化UI设计
- 完整的API集成

注意事项：
================================
1. 首次运行可能需要较长时间加载
2. 请确保网络连接正常
3. 建议定期备份配置文件
4. 如需升级，请保留config目录下的配置文件

版权信息：
================================
智慧食堂管理系统
Copyright © 2025 智慧食堂
保留所有权利
