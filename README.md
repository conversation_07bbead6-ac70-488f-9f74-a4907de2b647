# 智慧食堂管理系统

基于PyQt6开发的智慧食堂管理系统，采用现代化的玻璃态UI设计风格。

## 功能特性

### 一、学校用户功能
1. **系统登录** - 安全的用户认证系统
   - Token过期检查和自动登录
   - 历史账号记忆和快速选择
   - 认证信息持久化存储
2. **食谱管理** - 食谱的增删改查管理
3. **采购下单** - 食材采购订单管理
4. **订单管理（收货）** - 订单状态跟踪和收货确认
5. **仓储管理** - 库存管理和出入库记录
6. **上报管理** - 数据上报和统计
7. **答疑解惑** - 在线客服和问题解答
8. **码上举报（二维码）** - 二维码扫描举报功能
9. **大屏展示** - 数据可视化大屏

### 二、API接口支持
- 登录认证
- 仓库列表
- 项目列表
- 餐别列表
- 库存列表
- 出库提交
- 入库登记
- 打印记录

### 三、硬件接入
- 晨检仪
- 留样秤
- 出入库台秤

### 四、供应商用户
- 供应商食谱
- 在线结算
- 更新下单价

## 技术栈

- **前端框架**: PyQt6
- **UI设计**: Glassmorphism（玻璃态设计）
- **网络请求**: requests
- **数据格式**: JSON
- **编程语言**: Python 3.8+

## 安装和运行

### 1. 环境要求
- Python 3.8 或更高版本
- Windows 10/11 (推荐)

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置API地址
编辑 `config/app_config.json` 文件，设置正确的API地址：
```json
{
  "api": {
    "base_url": "https://your-api-server.com/st/steelyard/",
    "timeout": 10,
    "version": "1.8.13"
  }
}
```

### 4. 运行系统
```bash
python main.py
```

## 项目结构

```
MaiCui/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明
├── config/                # 配置文件目录
│   ├── settings.py        # 系统设置
│   ├── app_config.json    # 应用配置
│   └── GrilmorphismUI.json # UI设计规范
├── ui/                    # 用户界面模块
│   ├── __init__.py
│   ├── login_window.py    # 登录窗口
│   ├── main_window.py     # 主界面
│   ├── styles.py          # 样式定义
│   └── modules/           # 功能模块
│       └── recipe_module.py # 食谱管理
├── api/                   # API接口模块
│   ├── __init__.py
│   ├── auth_api.py        # 认证API
│   └── canteen_api.py     # 食堂管理API
├── utils/                 # 工具模块
│   ├── __init__.py
│   └── auth_manager.py    # 认证管理器
└── assets/                # 资源文件
    └── images/            # 图片资源
```

## 登录接口说明

### 接口地址
```
POST https://[ip:port]/st/steelyard/?op=login
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | String | 是 | 用户名 |
| password | String | 是 | 密码 |
| version | String | 是 | 版本号 |

### 响应格式
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "access_token": "ED0DBD864AF7FFE72FB4663F892C68B8",
    "picture_upload_url": "https://st.pcylsoft.com:9006/st/steelyard/?op=picture"
  },
  "total": 0
}
```

## 开发说明

### UI设计规范
系统采用Glassmorphism（玻璃态）设计风格，具有以下特点：
- 半透明背景
- 模糊效果（backdrop-filter）
- 柔和的边框和阴影
- 渐变色彩搭配
- 现代化的交互体验

### 代码规范
- 使用Python类型提示
- 遵循PEP 8代码风格
- 中英文注释并存
- 模块化设计

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系开发团队。
