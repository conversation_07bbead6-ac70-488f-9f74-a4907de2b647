#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证串口配置功能
Verify Serial Configuration
"""

import os
import json

def verify_config_files():
    """验证配置文件"""
    print("🔍 验证配置文件...")
    
    # 检查配置文件
    config_file = "config/app_config.json"
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查串口配置
        if 'serial' not in config:
            print("❌ 配置文件中缺少串口配置")
            return False
        
        serial_config = config['serial']
        required_keys = ['port', 'baudrate', 'timeout', 'auto_detect']
        
        for key in required_keys:
            if key not in serial_config:
                print(f"❌ 串口配置中缺少: {key}")
                return False
        
        print("✅ 配置文件验证通过")
        print(f"   端口: {serial_config['port']}")
        print(f"   波特率: {serial_config['baudrate']}")
        print(f"   超时: {serial_config['timeout']}")
        print(f"   自动检测: {serial_config['auto_detect']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件读取失败: {e}")
        return False

def verify_settings_module():
    """验证设置模块"""
    print("\n🔍 验证设置模块...")
    
    try:
        from config.settings import settings
        
        # 检查串口相关属性
        attrs = ['serial_port', 'serial_baudrate', 'serial_timeout', 'serial_auto_detect']
        
        for attr in attrs:
            if not hasattr(settings, attr):
                print(f"❌ 设置模块缺少属性: {attr}")
                return False
        
        print("✅ 设置模块验证通过")
        print(f"   端口: {settings.serial_port}")
        print(f"   波特率: {settings.serial_baudrate}")
        print(f"   超时: {settings.serial_timeout}")
        print(f"   自动检测: {settings.serial_auto_detect}")
        
        return True
        
    except Exception as e:
        print(f"❌ 设置模块验证失败: {e}")
        return False

def verify_serial_worker():
    """验证串口工作线程"""
    print("\n🔍 验证串口工作线程...")
    
    try:
        from ui.modules.weight_submission_module import SerialWorker
        
        # 创建实例测试
        worker = SerialWorker(port="COM1", baudrate=9600, timeout=1, auto_detect=True)
        
        # 检查属性
        required_attrs = ['port', 'baudrate', 'timeout', 'auto_detect', 'detected_port']
        
        for attr in required_attrs:
            if not hasattr(worker, attr):
                print(f"❌ SerialWorker缺少属性: {attr}")
                return False
        
        # 检查方法
        required_methods = ['detect_weight_scale_port', 'run']
        
        for method in required_methods:
            if not hasattr(worker, method):
                print(f"❌ SerialWorker缺少方法: {method}")
                return False
        
        print("✅ 串口工作线程验证通过")
        print(f"   支持可配置参数: ✅")
        print(f"   支持自动检测: ✅")
        
        return True
        
    except Exception as e:
        print(f"❌ 串口工作线程验证失败: {e}")
        return False

def verify_serial_dialog():
    """验证串口配置对话框"""
    print("\n🔍 验证串口配置对话框...")
    
    try:
        from ui.dialogs.serial_config_dialog import SerialConfigDialog, SerialDetectionWorker
        
        print("✅ 串口配置对话框验证通过")
        print(f"   SerialConfigDialog: ✅")
        print(f"   SerialDetectionWorker: ✅")
        
        return True
        
    except Exception as e:
        print(f"❌ 串口配置对话框验证失败: {e}")
        return False

def verify_main_window():
    """验证主窗口集成"""
    print("\n🔍 验证主窗口集成...")
    
    try:
        from ui.main_window import MainWindow
        
        # 检查方法
        required_methods = ['open_settings', 'open_serial_config']
        
        for method in required_methods:
            if not hasattr(MainWindow, method):
                print(f"❌ MainWindow缺少方法: {method}")
                return False
        
        print("✅ 主窗口集成验证通过")
        print(f"   设置功能: ✅")
        print(f"   串口配置: ✅")
        
        return True
        
    except Exception as e:
        print(f"❌ 主窗口集成验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 串口配置功能验证")
    print("=" * 50)
    
    tests = [
        ("配置文件", verify_config_files),
        ("设置模块", verify_settings_module),
        ("串口工作线程", verify_serial_worker),
        ("串口配置对话框", verify_serial_dialog),
        ("主窗口集成", verify_main_window)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name}验证失败")
        except Exception as e:
            print(f"❌ {test_name}验证异常: {e}")
    
    print("\n📊 验证结果")
    print("=" * 30)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有验证通过！串口配置功能已成功实现。")
        print("\n✨ 功能特性:")
        print("• 支持任意串口（不限于COM4）")
        print("• 自动检测重量秤设备")
        print("• 可配置波特率和超时时间")
        print("• 提供友好的配置界面")
        print("• 集成到主界面设置中")
    else:
        print("⚠️ 部分验证失败，请检查相关文件。")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    exit(main())
