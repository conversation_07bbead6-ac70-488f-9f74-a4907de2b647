#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
库存管理集成演示
Stock Management Integration Demo

演示如何将库存管理API与现有的串口重量获取和照片拍摄功能集成
"""

import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.stock_api import StockAPI
from api.auth_api import AuthAPI


class StockManagementDemo:
    """库存管理演示类"""
    
    def __init__(self):
        """初始化"""
        self.auth_api = AuthAPI("https://st.pcylsoft.com:9006/st/steelyard/")
        self.stock_api = StockAPI("https://st.pcylsoft.com:9006/st/steelyard/")
        self.is_logged_in = False
    
    def login(self, username: str, password: str) -> bool:
        """登录系统"""
        print("🔐 正在登录...")
        result = self.auth_api.login(username, password)
        
        if result.get('code') == 200:
            # 设置token到库存API
            self.stock_api.set_access_token(self.auth_api.access_token)
            self.is_logged_in = True
            print("✅ 登录成功")
            return True
        else:
            print(f"❌ 登录失败: {result.get('msg', '未知错误')}")
            return False
    
    def load_basic_data(self):
        """加载基础数据"""
        if not self.is_logged_in:
            print("❌ 请先登录")
            return None
        
        print("📊 正在加载基础数据...")
        
        # 获取仓库列表
        depots_result = self.stock_api.get_depot_list()
        if depots_result.get('code') != 200:
            print(f"❌ 获取仓库列表失败: {depots_result.get('msg')}")
            return None
        
        # 获取项目列表
        projects_result = self.stock_api.get_project_list()
        if projects_result.get('code') != 200:
            print(f"❌ 获取项目列表失败: {projects_result.get('msg')}")
            return None
        
        # 获取餐别列表
        meal_times_result = self.stock_api.get_meal_time_list()
        if meal_times_result.get('code') != 200:
            print(f"❌ 获取餐别列表失败: {meal_times_result.get('msg')}")
            return None
        
        # 获取库存列表
        stocks_result = self.stock_api.get_stock_list()
        if stocks_result.get('code') != 200:
            print(f"❌ 获取库存列表失败: {stocks_result.get('msg')}")
            return None
        
        basic_data = {
            'depots': depots_result.get('data', []),
            'projects': projects_result.get('data', []),
            'meal_times': meal_times_result.get('data', []),
            'stocks': stocks_result.get('data', [])
        }
        
        print("✅ 基础数据加载完成")
        self.print_basic_data_summary(basic_data)
        
        return basic_data
    
    def print_basic_data_summary(self, basic_data):
        """打印基础数据摘要"""
        print("\n📋 基础数据摘要:")
        
        print(f"📦 仓库数量: {len(basic_data['depots'])}")
        for depot in basic_data['depots']:
            print(f"  - {depot['code']}: {depot['name']}")
        
        print(f"\n📋 项目数量: {len(basic_data['projects'])}")
        for project in basic_data['projects']:
            print(f"  - {project['code']}: {project['name']}")
        
        print(f"\n🍽️  餐别数量: {len(basic_data['meal_times'])}")
        for meal_time in basic_data['meal_times']:
            print(f"  - {meal_time['value']}: {meal_time['name']}")
        
        print(f"\n📊 库存商品数量: {len(basic_data['stocks'])}")
        # 只显示前5个商品
        for i, stock in enumerate(basic_data['stocks'][:5]):
            print(f"  - {stock['code']}: {stock['name']} ({stock['quantity']}{stock['unit']})")
        if len(basic_data['stocks']) > 5:
            print(f"  ... 还有{len(basic_data['stocks']) - 5}个商品")
    
    def simulate_weight_from_serial(self) -> str:
        """模拟从串口获取重量"""
        print("⚖️  模拟从串口获取重量...")
        # 在实际应用中，这里会调用串口读取代码
        # 参考 ui/modules/weight_submission_module.py 中的串口处理代码
        simulated_weight = "7.2"
        print(f"📊 获取到重量: {simulated_weight}kg")
        return simulated_weight
    
    def simulate_photo_capture(self) -> str:
        """模拟拍摄照片"""
        print("📸 模拟拍摄照片...")
        # 在实际应用中，这里会调用摄像头拍照代码
        # 参考 ui/modules/camera_module.py 中的拍照功能
        simulated_photo_path = "23/638702976856623258.jpg;"
        print(f"📷 照片路径: {simulated_photo_path}")
        return simulated_photo_path
    
    def create_stock_out_request(self, basic_data, product_code: str):
        """创建出库申请"""
        print(f"\n📤 创建商品 {product_code} 的出库申请...")
        
        # 1. 获取重量（模拟串口）
        weight = self.simulate_weight_from_serial()
        
        # 2. 拍摄照片（模拟摄像头）
        photo_path = self.simulate_photo_capture()
        
        # 3. 创建出库明细
        detail = self.stock_api.create_stock_detail(
            code=product_code,
            quantity=weight,
            unit="市斤",
            path=photo_path
        )
        
        # 4. 准备出库数据
        today = datetime.now().strftime("%Y-%m-%d")
        depot_code = basic_data['depots'][0]['code'] if basic_data['depots'] else "S01"
        project_code = basic_data['projects'][0]['code'] if basic_data['projects'] else "P0001"
        time_type = basic_data['meal_times'][0]['value'] if basic_data['meal_times'] else 1
        
        # 5. 验证数据
        validation = self.stock_api.validate_stock_out_data(
            datetime=today,
            depot_code=depot_code,
            project_code=project_code,
            time_type=time_type,
            people=100,
            details=[detail]
        )
        
        if not validation['valid']:
            print("❌ 数据验证失败:")
            for error in validation['errors']:
                print(f"  - {error}")
            return None
        
        print("✅ 数据验证通过")
        
        # 6. 提交出库申请
        print("📤 正在提交出库申请...")
        result = self.stock_api.submit_stock_out(
            datetime=today,
            depot_code=depot_code,
            project_code=project_code,
            time_type=time_type,
            people=100,
            details=[detail]
        )
        
        if result.get('code') == 200:
            print(f"✅ 出库申请提交成功，出库单号: {result.get('msg')}")
            return result.get('msg')
        else:
            print(f"❌ 出库申请提交失败: {result.get('msg')}")
            return None
    
    def run_demo(self):
        """运行演示"""
        print("🎯 库存管理集成演示")
        print("=" * 60)
        
        # 注意：这里需要真实的用户名和密码
        print("⚠️  请提供真实的登录凭据进行测试")
        print("示例用法:")
        print("demo = StockManagementDemo()")
        print("demo.login('your_username', 'your_password')")
        print("basic_data = demo.load_basic_data()")
        print("demo.create_stock_out_request(basic_data, '13010405')")
        print()
        
        print("🔧 集成要点:")
        print("1. 串口重量获取 - 参考 ui/modules/weight_submission_module.py")
        print("2. 摄像头拍照 - 参考 ui/modules/camera_module.py")
        print("3. 图片上传 - 参考 api/weight_api.py 的 submit_picture 方法")
        print("4. 数据验证 - 使用 validate_stock_out_data 方法")
        print("5. 出库提交 - 使用 submit_stock_out 方法")
        print()
        
        print("📋 完整工作流程:")
        print("1. 用户登录获取token")
        print("2. 加载基础数据（仓库、项目、餐别、库存）")
        print("3. 选择商品进行出库")
        print("4. 通过串口获取重量")
        print("5. 通过摄像头拍摄照片")
        print("6. 创建出库明细")
        print("7. 验证出库数据")
        print("8. 提交出库申请")
        print("9. 获取出库单号")


def main():
    """主函数"""
    demo = StockManagementDemo()
    demo.run_demo()


if __name__ == "__main__":
    main()
