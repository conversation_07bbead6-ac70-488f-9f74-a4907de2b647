#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口地址测试脚本
API Endpoints Test Script
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.weight_api import WeightAPI

def test_api_urls():
    """测试API接口地址构建"""
    print("🔗 API接口地址测试")
    print("=" * 60)
    
    # 测试不同的base_url格式
    test_cases = [
        "https://st.pcylsoft.com:9006",
        "https://st.pcylsoft.com:9006/",
        "https://st.pcylsoft.com:9006/st/steelyard",
        "https://st.pcylsoft.com:9006/st/steelyard/",
    ]
    
    for i, base_url in enumerate(test_cases, 1):
        print(f"\n📋 测试案例 {i}: {base_url}")
        
        try:
            # 创建API实例
            api = WeightAPI(base_url)
            
            # 模拟构建图片提交URL
            if api.base_url.endswith('/st/steelyard/') or api.base_url.endswith('/st/steelyard'):
                url = api.base_url.rstrip('/') + '/'
            else:
                url = f"{api.base_url}/st/steelyard/"
            
            # 添加参数
            picture_url = f"{url}?op=picture"
            weight_url = f"{url}?op=weight"
            
            print(f"   📤 重量提交接口: {weight_url}")
            print(f"   📸 图片提交接口: {picture_url}")
            
            # 验证URL格式
            expected_picture_url = "https://st.pcylsoft.com:9006/st/steelyard/?op=picture"
            expected_weight_url = "https://st.pcylsoft.com:9006/st/steelyard/?op=weight"
            
            if picture_url == expected_picture_url:
                print("   ✅ 图片接口地址正确")
            else:
                print(f"   ❌ 图片接口地址错误，期望: {expected_picture_url}")
            
            if weight_url == expected_weight_url:
                print("   ✅ 重量接口地址正确")
            else:
                print(f"   ❌ 重量接口地址错误，期望: {expected_weight_url}")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")

def test_picture_api_method():
    """测试图片提交API方法"""
    print("\n🖼️  图片提交API方法测试")
    print("=" * 60)
    
    try:
        # 创建API实例
        api = WeightAPI("https://st.pcylsoft.com:9006/st/steelyard/")
        
        # 检查方法是否存在
        if hasattr(api, 'submit_picture'):
            print("✅ submit_picture 方法存在")
            
            # 获取方法文档
            method_doc = api.submit_picture.__doc__
            if method_doc:
                print("📖 方法文档:")
                print(method_doc.strip())
            
            # 检查方法参数
            import inspect
            sig = inspect.signature(api.submit_picture)
            print(f"📝 方法签名: submit_picture{sig}")
            
            print("\n🔧 预期调用方式:")
            print("api.submit_picture(")
            print("    order_detail_id=1058706,")
            print("    batch=0,")
            print("    image_path='photos/photo_20241201_143022.jpg'")
            print(")")
            
        else:
            print("❌ submit_picture 方法不存在")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_api_configuration():
    """测试API配置"""
    print("\n⚙️  API配置测试")
    print("=" * 60)
    
    try:
        # 检查配置文件
        config_file = "config/app_config.json"
        if os.path.exists(config_file):
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            api_config = config.get('api', {})
            base_url = api_config.get('base_url', '')
            
            print(f"📄 配置文件: {config_file}")
            print(f"🔗 配置的base_url: {base_url}")
            
            # 验证配置的URL
            if base_url == "https://st.pcylsoft.com:9006/st/steelyard/":
                print("✅ 配置文件中的API地址正确")
            else:
                print("⚠️  配置文件中的API地址可能需要更新")
                print("   建议设置为: https://st.pcylsoft.com:9006/st/steelyard/")
        else:
            print(f"❌ 配置文件不存在: {config_file}")
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")

def main():
    """主函数"""
    print("🎯 API接口地址验证工具")
    print("=" * 60)
    print("📋 验证内容:")
    print("1. API接口地址构建逻辑")
    print("2. 图片提交方法可用性")
    print("3. 配置文件设置")
    print()
    
    # 运行测试
    test_api_urls()
    test_picture_api_method()
    test_api_configuration()
    
    print("\n🎉 测试完成！")
    print("\n📖 接口说明:")
    print("重量提交: POST https://st.pcylsoft.com:9006/st/steelyard/?op=weight")
    print("图片提交: POST https://st.pcylsoft.com:9006/st/steelyard/?op=picture")
    print()
    print("🔑 认证方式:")
    print("Authorization: Bearer <access_token>")
    print()
    print("📤 图片提交参数:")
    print("- id: 订单明细ID (Integer)")
    print("- batch: 批次号 (Number, 从0开始)")
    print("- file: 图片文件 (multipart/form-data)")

if __name__ == "__main__":
    main()
