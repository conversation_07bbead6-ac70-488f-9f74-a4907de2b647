# 📸 摄像头拍照功能集成完成

## ✅ 功能概览

已成功为智慧食堂管理系统的重量提交模块添加了完整的摄像头拍照和图片提交功能。

### 🎯 核心功能

1. **USB摄像头集成**
   - 实时预览画面
   - 手动拍照功能
   - 自动拍照（提交时）
   - 摄像头状态监控

2. **图片提交API**
   - 多格式图片上传
   - 批次管理（从0开始递增）
   - 完整错误处理
   - Token认证支持

3. **一体化提交流程**
   - 重量数据提交
   - 自动拍照
   - 图片上传
   - 状态反馈

## 🔗 API接口详情

### 图片提交接口
- **URL**: `https://st.pcylsoft.com:9006/st/steelyard/?op=picture`
- **方法**: POST
- **Content-Type**: `multipart/form-data`
- **认证**: `Authorization: Bearer <access_token>`

### 请求参数
```
id: 订单明细ID (Integer)
batch: 批次号 (Number, 从0开始)
file: 图片文件 (File)
```

### 响应示例
```json
{
    "code": 200,
    "msg": "23/638894961240052462.jpg",
    "total": 0
}
```

## 📋 提交流程

1. **重量数据提交**
   ```
   POST https://st.pcylsoft.com:9006/st/steelyard/?op=weight
   ```

2. **自动拍照**（如果摄像头开启）
   - 检查摄像头状态
   - 生成时间戳文件名
   - 保存到 `photos/` 目录

3. **图片提交**
   ```
   POST https://st.pcylsoft.com:9006/st/steelyard/?op=picture
   ```

4. **结果反馈**
   - 显示提交状态
   - 清理临时数据
   - 准备下次操作

## 🚀 使用方法

### 1. 安装依赖
```bash
python install_camera_dependencies.py
```

### 2. 测试功能
```bash
python test_camera_integration.py
```

### 3. 实际使用
1. 启动摄像头：点击"📹 启动"按钮
2. 添加重量：通过串口或手动添加
3. 拍照（可选）：点击"📸 拍照"按钮
4. 提交数据：点击"📤 提交"按钮

## 🔧 技术实现

### 文件结构
```
ui/modules/
├── camera_module.py          # 摄像头模块
└── weight_submission_module.py  # 重量提交模块（已更新）

api/
└── weight_api.py            # 重量和图片API（已更新）

photos/                      # 图片保存目录
├── photo_20241201_143022.jpg
└── auto_photo_20241201_143045.jpg
```

### 核心类和方法

#### CameraModule
- `start_camera()`: 启动摄像头
- `capture_photo()`: 手动拍照
- `cleanup()`: 清理资源

#### WeightAPI
- `submit_weight()`: 提交重量数据
- `submit_picture()`: 提交图片文件

#### WeightSubmissionModule
- `handle_photo_submission()`: 处理图片提交
- `on_photo_captured()`: 拍照完成处理

## 🎯 智能特性

### 自适应拍照策略
- **已有照片** → 使用现有照片
- **摄像头开启** → 自动拍照
- **无摄像头** → 仅提交重量

### 批次管理
- 自动递增批次号
- 支持同一商品多次提交
- 批次信息记录在API中

### 错误处理
- 摄像头连接失败处理
- 图片文件大小限制（10MB）
- 网络异常重试机制
- 部分成功状态处理

## 📸 硬件要求

- **摄像头**: USB摄像头（支持UVC协议）
- **分辨率**: 建议320x240或更高
- **格式**: JPEG输出
- **驱动**: Windows自动识别

## 🔍 测试验证

### 测试脚本
- `test_camera_integration.py`: 完整功能测试
- `install_camera_dependencies.py`: 依赖安装
- `verify_api_url.py`: API地址验证

### 测试步骤
1. 运行依赖安装脚本
2. 连接USB摄像头
3. 运行集成测试脚本
4. 验证各项功能

## ⚠️ 注意事项

1. **摄像头要求**
   - 确保USB摄像头正确连接
   - 安装必要的驱动程序
   - 避免被其他程序占用

2. **API认证**
   - 需要有效的access_token
   - Token过期需要重新登录
   - 网络连接稳定

3. **文件管理**
   - 图片自动保存到photos/目录
   - 文件名包含时间戳
   - 定期清理旧图片

## 🎉 功能完成

✅ USB摄像头实时预览  
✅ 手动拍照功能  
✅ 自动拍照（提交时）  
✅ 图片上传API集成  
✅ 重量+图片一体化提交  
✅ 批次管理系统  
✅ 完整错误处理  
✅ 界面优化和压缩  

现在您的系统具备了完整的重量数据采集、拍照和上传功能，用户可以一键完成整个数据提交流程！
