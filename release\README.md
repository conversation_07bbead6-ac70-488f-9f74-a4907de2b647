# 智慧食堂管理系统 v1.0.0

## 📋 项目简介

智慧食堂管理系统是一个基于PyQt6开发的现代化食堂管理解决方案，采用玻璃态(Glassmorphism)UI设计风格，为学校食堂提供全面的数字化管理功能。

## ✨ 主要特色

- 🎨 **现代化UI设计** - 采用最新的Glassmorphism设计风格
- 🔐 **安全认证系统** - 完整的用户登录和权限管理
- 📱 **响应式界面** - 支持不同分辨率和窗口大小
- 🌐 **完整API集成** - 标准的RESTful API接口
- ⚡ **免安装运行** - 无需Python环境，双击即用

## 🚀 快速开始

### 方式一：单文件版本（推荐）
1. 双击 `智慧食堂管理系统.exe` 启动程序
2. 或者双击 `启动程序.bat` 使用启动脚本

### 方式二：目录版本
1. 进入 `智慧食堂管理系统_目录版` 文件夹
2. 双击其中的 `智慧食堂管理系统.exe`

## 📁 文件结构

```
release/
├── 智慧食堂管理系统.exe          # 单文件可执行版本（推荐）
├── 智慧食堂管理系统_目录版/       # 目录版本
├── config/                       # 配置文件目录
│   ├── app_config.json          # 应用配置
│   ├── GrilmorphismUI.json      # UI设计规范
│   └── settings.py              # 系统设置
├── 启动程序.bat                  # 快速启动脚本
├── 使用说明.txt                  # 详细使用说明
└── README.md                     # 项目说明文件
```

## ⚙️ 系统要求

- **操作系统**: Windows 7 及以上版本
- **内存**: 建议 4GB 以上
- **硬盘空间**: 至少 100MB 可用空间
- **网络**: 需要网络连接以访问API服务
- **Python环境**: 无需安装（已打包）

## 🔧 配置说明

### API服务器配置
编辑 `config/app_config.json` 文件：

```json
{
  "api": {
    "base_url": "https://your-api-server.com/st/steelyard/"
  },
  "ui": {
    "window_width": 1200,
    "window_height": 800,
    "remember_username": true
  }
}
```

## 🎯 主要功能

### 已实现功能 ✅
- [x] 用户登录认证
- [x] 主界面框架
- [x] 食谱管理模块
- [x] API接口集成
- [x] 配置管理系统

### 计划功能 🚧
- [ ] 采购下单模块
- [ ] 订单管理模块
- [ ] 仓储管理模块
- [ ] 上报管理模块
- [ ] 答疑解惑模块

## 🐛 故障排除

### 常见问题

**Q: 程序无法启动？**
A: 检查系统版本，确保为Windows 7及以上，并有足够内存空间

**Q: 登录失败？**
A: 检查网络连接，确认API服务器地址配置正确

**Q: 界面显示异常？**
A: 调整系统显示缩放设置，建议使用100%缩放

**Q: 程序运行缓慢？**
A: 建议使用目录版本，或增加系统内存

## 📞 技术支持

如遇到问题，请联系系统管理员或技术支持团队。

## 📝 更新日志

### v1.0.0 (2025-07-30)
- 🎉 初始版本发布
- ✨ 实现基础登录和食谱管理功能
- 🎨 采用现代化UI设计
- 🌐 完整的API集成

## 📄 版权信息

智慧食堂管理系统  
Copyright © 2025 智慧食堂  
保留所有权利

---

**享受智慧食堂管理的便捷体验！** 🍽️✨
