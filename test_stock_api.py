#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
库存管理API测试
Stock Management API Test
"""

import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.stock_api import StockAPI


def test_stock_api():
    """测试库存管理API"""
    print("🧪 库存管理API测试")
    print("=" * 60)
    
    # 创建API实例
    api = StockAPI("https://st.pcylsoft.com:9006/st/steelyard/")
    
    # 注意：这里需要先登录获取token
    print("⚠️  请先使用认证API登录获取access_token")
    print("示例：")
    print("from api.auth_api import AuthAPI")
    print("auth_api = AuthAPI('https://st.pcylsoft.com:9006/st/steelyard/')")
    print("login_result = auth_api.login('your_username', 'your_password')")
    print("api.set_access_token(auth_api.access_token)")
    print()
    
    # 测试仓库列表
    print("📦 测试仓库列表接口:")
    print("URL: https://st.pcylsoft.com:9006/st/steelyard/?op=stock_depot")
    print("方法: POST")
    print("Content-Type: application/x-www-form-urlencoded")
    print("代码示例:")
    print("result = api.get_depot_list()")
    print("预期返回格式:")
    print("""{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "code": "S01",
            "name": "食堂仓库",
            "number": "01"
        },
        {
            "code": "S02", 
            "name": "调料仓库",
            "number": "02"
        }
    ],
    "total": 2
}""")
    print()
    
    # 测试项目列表
    print("📋 测试项目列表接口:")
    print("URL: https://st.pcylsoft.com:9006/st/steelyard/?op=stock_project")
    print("方法: POST")
    print("Content-Type: application/x-www-form-urlencoded")
    print("代码示例:")
    print("result = api.get_project_list()")
    print("预期返回格式:")
    print("""{
    "code": 200,
    "msg": "success", 
    "data": [
        {
            "code": "P0001",
            "name": "非营养餐"
        },
        {
            "code": "P0002",
            "name": "营养餐"
        }
    ],
    "total": 2
}""")
    print()
    
    # 测试餐别列表
    print("🍽️  测试餐别列表接口:")
    print("URL: https://st.pcylsoft.com:9006/st/steelyard/?op=stock_time")
    print("方法: POST")
    print("Content-Type: application/x-www-form-urlencoded")
    print("代码示例:")
    print("result = api.get_meal_time_list()")
    print("预期返回格式:")
    print("""{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "value": 1,
            "name": "早餐"
        },
        {
            "value": 2,
            "name": "早点"
        },
        {
            "value": 4,
            "name": "午餐"
        },
        {
            "value": 8,
            "name": "午点"
        },
        {
            "value": 16,
            "name": "晚餐"
        },
        {
            "value": 32,
            "name": "晚点"
        }
    ],
    "total": 6
}""")
    print()
    
    # 测试库存列表
    print("📊 测试库存列表接口:")
    print("URL: https://st.pcylsoft.com:9006/st/steelyard/?op=stocks")
    print("方法: POST")
    print("Content-Type: application/x-www-form-urlencoded")
    print("代码示例:")
    print("result = api.get_stock_list()")
    print("预期返回格式:")
    print("""{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "code": "13010405",
            "name": "小白菜",
            "unit": "市斤",
            "brand": null,
            "spec": null,
            "quantity": 33.3
        },
        {
            "code": "13010406",
            "name": "西红柿", 
            "unit": "市斤",
            "brand": null,
            "spec": null,
            "quantity": 3.8
        }
    ],
    "total": 30
}""")
    print()


def demo_stock_out_submission():
    """演示出库提交"""
    print("📤 出库提交接口演示:")
    print("URL: https://st.pcylsoft.com:9006/st/steelyard/?op=stock_submit")
    print("方法: POST")
    print("Content-Type: application/json")
    print()
    
    print("🔧 完整使用示例:")
    print("""
# 1. 创建API实例并登录
from api.stock_api import StockAPI
from api.auth_api import AuthAPI

# 登录获取token
auth_api = AuthAPI("https://st.pcylsoft.com:9006/st/steelyard/")
login_result = auth_api.login("your_username", "your_password")

# 创建库存API实例
stock_api = StockAPI("https://st.pcylsoft.com:9006/st/steelyard/")
stock_api.set_access_token(auth_api.access_token)

# 2. 获取基础数据
depots = stock_api.get_depot_list()  # 获取仓库列表
projects = stock_api.get_project_list()  # 获取项目列表
meal_times = stock_api.get_meal_time_list()  # 获取餐别列表
stocks = stock_api.get_stock_list()  # 获取库存列表

# 3. 准备出库明细（结合串口重量和照片）
details = []

# 假设从串口获取到重量7.2kg，从摄像头获取到照片路径
weight_from_serial = "7.2"  # 从串口获取的重量
photo_path = "23/638702976856623258.jpg;"  # 从照片上传获取的路径

# 创建出库明细
detail = stock_api.create_stock_detail(
    code="13010405",  # 商品代码
    quantity=weight_from_serial,  # 重量（从串口获取）
    unit="市斤",  # 固定单位
    path=photo_path  # 照片路径
)
details.append(detail)

# 4. 提交出库申请
result = stock_api.submit_stock_out(
    datetime="2024-12-20",  # 出库日期
    depot_code="S01",  # 仓库代码（从仓库列表获取）
    project_code="P0001",  # 项目代码（从项目列表获取）
    time_type=1,  # 餐别值（从餐别列表获取，1=早餐）
    people=100,  # 人数
    details=details  # 出库明细
)

print("出库结果:", result)
# 预期返回: {"code": 200, "msg": "YO25073117941", "total": 0}
""")
    print()
    
    print("📋 数据验证示例:")
    api = StockAPI("https://st.pcylsoft.com:9006/st/steelyard/")
    
    # 创建测试明细
    details = [
        api.create_stock_detail("13010405", "7.2", "市斤", "23/638702976856623258.jpg;")
    ]
    
    # 验证数据
    validation = api.validate_stock_out_data(
        datetime="2024-12-20",
        depot_code="S01", 
        project_code="P0001",
        time_type=1,
        people=100,
        details=details
    )
    
    print(f"数据验证结果: {validation}")
    
    # 获取餐别名称
    meal_name = api.get_meal_time_name(1)
    print(f"餐别1对应: {meal_name}")


def main():
    """主函数"""
    print("🎯 库存管理API接口测试工具")
    print("=" * 60)
    print("📋 测试内容:")
    print("1. 仓库列表接口")
    print("2. 项目列表接口") 
    print("3. 餐别列表接口")
    print("4. 库存列表接口")
    print("5. 出库提交接口")
    print()
    
    # 运行测试
    test_stock_api()
    demo_stock_out_submission()
    
    print("\n🎉 测试完成！")
    print("\n📖 接口说明:")
    print("仓库列表: POST https://st.pcylsoft.com:9006/st/steelyard/?op=stock_depot")
    print("项目列表: POST https://st.pcylsoft.com:9006/st/steelyard/?op=stock_project")
    print("餐别列表: POST https://st.pcylsoft.com:9006/st/steelyard/?op=stock_time")
    print("库存列表: POST https://st.pcylsoft.com:9006/st/steelyard/?op=stocks")
    print("出库提交: POST https://st.pcylsoft.com:9006/st/steelyard/?op=stock_submit")
    print()
    print("🔑 认证方式:")
    print("Authorization: <access_token> (放在Header中)")
    print()
    print("📤 出库提交特殊说明:")
    print("- Content-Type: application/json")
    print("- 重量通过串口获取")
    print("- 照片通过摄像头拍摄并上传")
    print("- 单位固定为'市斤'")


if __name__ == "__main__":
    main()
