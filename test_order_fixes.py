#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试订单模块修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtCore import QTimer
from ui.modules.order_module import OrderModule
from api.order_api import OrderAPI

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("订单模块测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置背景
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
            }
        """)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 创建订单模块
        self.order_module = OrderModule()
        layout.addWidget(self.order_module)
        
        # 模拟设置API（用于测试UI，不实际调用API）
        print("创建测试API实例...")
        api = OrderAPI("https://test.example.com/")
        self.order_module.set_api(api)
        
        # 模拟一些测试数据
        self.setup_test_data()
        
    def setup_test_data(self):
        """设置测试数据"""
        test_orders = [
            {
                'trade_no': 'TEST001',
                'circulate_name': '测试供应商A',
                'collect_time': '2023-12-01 10:30:00',
                'state': 0,
                'item_count': 3,
                'buy_quantity': '10',
                'deliver_quantity': '8.5',
                'goods': [
                    {
                        'name': '苹果',
                        'code': 'AP001',
                        'buy_quantity': '5',
                        'deliver_quantity': '4.5',
                        'buy_unit': '公斤'
                    },
                    {
                        'name': '香蕉',
                        'code': 'BN001',
                        'buy_quantity': '3',
                        'deliver_quantity': '2.8',
                        'buy_unit': '公斤'
                    }
                ]
            },
            {
                'trade_no': 'TEST002',
                'circulate_name': '测试供应商B',
                'collect_time': '2023-12-01 14:20:00',
                'state': 1,
                'item_count': 2,
                'buy_quantity': '15',
                'deliver_quantity': '15',
                'goods': [
                    {
                        'name': '白菜',
                        'code': 'CB001',
                        'buy_quantity': '10',
                        'deliver_quantity': '10',
                        'buy_unit': '公斤'
                    }
                ]
            }
        ]
        
        # 直接设置测试数据到模块
        self.order_module.current_orders = test_orders
        self.order_module.populate_order_table(test_orders)
        print(f"设置了 {len(test_orders)} 条测试订单")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    window = TestWindow()
    window.show()
    
    print("测试窗口已显示")
    print("请测试以下功能：")
    print("1. 界面是否使用了Glassmorphism样式")
    print("2. 点击订单行是否会显示详情（不应该闪退）")
    print("3. 筛选功能是否正常工作")
    print("4. 按钮样式是否正确")
    
    # 设置定时器自动退出（用于自动化测试）
    timer = QTimer()
    timer.timeout.connect(lambda: (print("测试完成，程序即将退出"), app.quit()))
    timer.start(30000)  # 30秒后自动退出
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
