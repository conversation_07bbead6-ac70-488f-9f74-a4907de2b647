#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试全局串口管理器
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_global_serial_manager_import():
    """测试全局串口管理器导入"""
    print("🔍 测试全局串口管理器导入")
    print("=" * 40)
    
    try:
        from ui.global_serial_manager import GlobalSerialManager, global_serial_manager
        print("✅ GlobalSerialManager类导入成功")
        print("✅ global_serial_manager实例导入成功")
        
        # 检查单例模式
        manager1 = GlobalSerialManager()
        manager2 = GlobalSerialManager()
        
        if manager1 is manager2:
            print("✅ 单例模式工作正常")
        else:
            print("❌ 单例模式失败")
        
        # 检查关键方法
        required_methods = [
            'add_subscriber', 'remove_subscriber', 
            'start_connection', 'stop_connection',
            'get_status'
        ]
        
        for method in required_methods:
            if hasattr(global_serial_manager, method):
                print(f"✅ 方法存在: {method}")
            else:
                print(f"❌ 缺少方法: {method}")
        
        return True
        
    except Exception as e:
        print(f"❌ 全局串口管理器导入失败: {e}")
        return False

def test_subscriber_management():
    """测试订阅者管理"""
    print("\n🔍 测试订阅者管理")
    print("=" * 40)
    
    try:
        from ui.global_serial_manager import global_serial_manager
        
        # 测试添加订阅者
        def dummy_callback(data):
            print(f"收到数据: {data}")
        
        callbacks = {
            'weight_received': dummy_callback,
            'connection_status': dummy_callback,
            'error_occurred': dummy_callback
        }
        
        # 添加订阅者
        global_serial_manager.add_subscriber('test_module_1', callbacks)
        global_serial_manager.add_subscriber('test_module_2', callbacks)
        
        status = global_serial_manager.get_status()
        print(f"✅ 添加订阅者后状态: {status}")
        
        if status['subscriber_count'] == 2:
            print("✅ 订阅者计数正确")
        else:
            print(f"❌ 订阅者计数错误: 期望2，实际{status['subscriber_count']}")
        
        # 移除订阅者
        global_serial_manager.remove_subscriber('test_module_1')
        
        status = global_serial_manager.get_status()
        print(f"✅ 移除订阅者后状态: {status}")
        
        if status['subscriber_count'] == 1:
            print("✅ 订阅者移除正确")
        else:
            print(f"❌ 订阅者移除错误: 期望1，实际{status['subscriber_count']}")
        
        # 清理
        global_serial_manager.remove_subscriber('test_module_2')
        
        return True
        
    except Exception as e:
        print(f"❌ 订阅者管理测试失败: {e}")
        return False

def test_connection_status():
    """测试连接状态"""
    print("\n🔍 测试连接状态")
    print("=" * 40)
    
    try:
        from ui.global_serial_manager import global_serial_manager
        
        # 获取初始状态
        status = global_serial_manager.get_status()
        print(f"初始状态: {status}")
        
        if not status['connected']:
            print("✅ 初始状态正确（未连接）")
        else:
            print("❌ 初始状态错误（应该未连接）")
        
        # 检查状态结构
        required_keys = ['connected', 'port', 'subscriber_count', 'subscribers']
        for key in required_keys:
            if key in status:
                print(f"✅ 状态包含: {key}")
            else:
                print(f"❌ 状态缺少: {key}")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接状态测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 全局串口管理器测试")
    print("=" * 50)
    
    # 测试导入
    import_ok = test_global_serial_manager_import()
    subscriber_ok = test_subscriber_management()
    status_ok = test_connection_status()
    
    print("\n" + "=" * 50)
    print("🎉 测试总结")
    
    if import_ok and subscriber_ok and status_ok:
        print("✅ 所有测试通过")
        print("\n📝 全局串口管理器功能:")
        print("1. ✅ 单例模式正常工作")
        print("2. ✅ 订阅者管理功能完整")
        print("3. ✅ 连接状态管理正确")
        print("4. ✅ 方法接口完整")
        
        print("\n🔧 解决的问题:")
        print("- 串口资源冲突")
        print("- 多模块共享串口")
        print("- 统一的连接管理")
        print("- 订阅者模式数据分发")
        
        print("\n🚀 预期效果:")
        print("- 订单管理和出库管理可以同时使用串口")
        print("- 系统设置测试连接不会冲突")
        print("- 串口状态统一显示")
        print("- 重量数据实时共享")
        
        print("\n📋 使用说明:")
        print("1. 程序启动后，订单管理模块自动连接串口")
        print("2. 出库管理模块显示共享连接状态")
        print("3. 系统设置测试连接会提示冲突并处理")
        print("4. 所有模块共享重量数据")
        
    else:
        print("❌ 部分测试失败")
        if not import_ok:
            print("- 全局串口管理器导入有问题")
        if not subscriber_ok:
            print("- 订阅者管理有问题")
        if not status_ok:
            print("- 连接状态管理有问题")

if __name__ == "__main__":
    main()
