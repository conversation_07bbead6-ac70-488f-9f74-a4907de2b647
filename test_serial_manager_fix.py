#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试串口管理器修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_serial_manager_import():
    """测试串口管理器导入"""
    print("🔍 测试串口管理器导入")
    print("=" * 30)
    
    try:
        from ui.serial_manager import SerialManager, serial_manager
        print("✅ SerialManager类导入成功")
        print("✅ serial_manager实例导入成功")
        
        # 检查单例模式
        manager1 = SerialManager()
        manager2 = SerialManager()
        
        if manager1 is manager2:
            print("✅ 单例模式工作正常")
        else:
            print("❌ 单例模式失败")
        
        # 检查关键方法
        required_methods = [
            'add_subscriber', 'remove_subscriber', 
            'start_serial_connection', 'stop_serial_connection',
            'get_connection_status'
        ]
        
        for method in required_methods:
            if hasattr(serial_manager, method):
                print(f"✅ 方法存在: {method}")
            else:
                print(f"❌ 缺少方法: {method}")
        
        return True
        
    except Exception as e:
        print(f"❌ 串口管理器导入失败: {e}")
        return False

def test_stock_out_module_integration():
    """测试出库管理模块集成"""
    print("\n🔍 测试出库管理模块集成")
    print("=" * 30)
    
    try:
        from ui.modules.stock_out_module import StockOutModule
        
        print("✅ StockOutModule导入成功")
        
        # 检查串口管理器相关方法
        required_methods = [
            'setup_serial_manager',
            'connect_to_serial_manager', 
            'cleanup_serial_connection'
        ]
        
        for method in required_methods:
            if hasattr(StockOutModule, method):
                print(f"✅ 方法存在: {method}")
            else:
                print(f"❌ 缺少方法: {method}")
        
        return True
        
    except Exception as e:
        print(f"❌ 出库管理模块集成测试失败: {e}")
        return False

def test_serial_manager_functionality():
    """测试串口管理器功能"""
    print("\n🔍 测试串口管理器功能")
    print("=" * 30)
    
    try:
        from ui.serial_manager import serial_manager
        
        # 测试初始状态
        status = serial_manager.get_connection_status()
        print(f"✅ 初始状态: connected={status['connected']}, subscribers={status['subscriber_count']}")
        
        # 测试订阅者管理
        test_callbacks = {
            'weight_received': lambda w: print(f"Weight: {w}"),
            'connection_status': lambda c: print(f"Connected: {c}"),
            'error_occurred': lambda e: print(f"Error: {e}"),
            'port_detected': lambda p: print(f"Port: {p}")
        }
        
        # 添加订阅者
        serial_manager.add_subscriber('test_module', test_callbacks)
        status = serial_manager.get_connection_status()
        print(f"✅ 添加订阅者后: subscribers={status['subscriber_count']}")
        
        # 移除订阅者
        serial_manager.remove_subscriber('test_module')
        status = serial_manager.get_connection_status()
        print(f"✅ 移除订阅者后: subscribers={status['subscriber_count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 串口管理器功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始串口管理器修复测试")
    print("=" * 50)
    
    # 测试导入
    import_ok = test_serial_manager_import()
    integration_ok = test_stock_out_module_integration()
    functionality_ok = test_serial_manager_functionality()
    
    print("\n" + "=" * 50)
    print("🎉 测试总结")
    
    if import_ok and integration_ok and functionality_ok:
        print("✅ 所有测试通过")
        print("\n📝 修复内容:")
        print("1. ✅ 创建了串口管理器 (SerialManager)")
        print("2. ✅ 实现了单例模式避免重复实例")
        print("3. ✅ 实现了订阅者模式共享串口数据")
        print("4. ✅ 修改出库管理模块使用串口管理器")
        print("5. ✅ 添加了连接状态和错误处理")
        
        print("\n🔧 串口管理器功能:")
        print("- 单例模式管理串口资源")
        print("- 订阅者模式分发数据")
        print("- 统一的连接状态管理")
        print("- 自动处理端口冲突")
        print("- 支持多模块共享")
        
        print("\n🚀 预期效果:")
        print("- 解决串口端口冲突问题")
        print("- 订单管理和出库管理共享串口")
        print("- 统一的连接状态显示")
        print("- 更好的错误处理和用户提示")
        
        print("\n📋 使用说明:")
        print("1. 在任一模块中连接串口")
        print("2. 其他模块自动共享连接")
        print("3. 显示共享状态和订阅者数量")
        print("4. 断开连接影响所有模块")
        
    else:
        print("❌ 部分测试失败")
        if not import_ok:
            print("- 串口管理器导入有问题")
        if not integration_ok:
            print("- 出库管理模块集成有问题")
        if not functionality_ok:
            print("- 串口管理器功能有问题")

if __name__ == "__main__":
    main()
