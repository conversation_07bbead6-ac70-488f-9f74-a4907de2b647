#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版主程序
Simplified Main Program
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """主程序入口"""
    print("启动智慧食堂管理系统...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QFont
        
        # 创建应用程序实例
        app = QApplication(sys.argv)
        
        # 设置应用程序属性
        app.setApplicationName("智慧食堂管理系统")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("智慧食堂")
        
        # 设置全局字体
        try:
            font = QFont("Microsoft YaHei", 10)
            app.setFont(font)
        except:
            # 如果字体不可用，使用默认字体
            pass
        
        print("创建登录窗口...")
        
        # 导入并创建登录窗口
        from ui.login_window import LoginWindow
        login_window = LoginWindow()
        login_window.show()
        
        print("系统启动成功！")
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装所需依赖: pip install PyQt6 requests")
        
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
