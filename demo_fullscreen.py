#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全屏功能演示
Fullscreen Feature Demo
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_fullscreen():
    """演示全屏功能"""
    print("🖥️  全屏功能演示")
    print("=" * 50)
    
    try:
        from PyQt6.QtWidgets import QApplication, QMessageBox
        from ui.main_window import MainWindow
        from config.settings import settings
        
        # 创建应用程序实例
        app = QApplication(sys.argv)
        app.setApplicationName("智慧食堂管理系统")
        
        print("✅ 应用程序创建成功")
        print(f"📋 应用名称: {settings.app_name}")
        print(f"🖥️  全屏设置: {settings.fullscreen}")
        
        # 创建主窗口
        main_window = MainWindow("demo_token")
        print("✅ 主窗口创建成功")
        
        # 显示窗口
        main_window.show()
        print("✅ 主窗口已显示")
        
        if settings.fullscreen:
            print("🎯 全屏模式已启用")
            print("💡 提示:")
            print("  - 按 ESC 键退出全屏")
            print("  - 按 F11 键切换全屏")
        else:
            print("🪟 窗口模式")
            print(f"📐 窗口尺寸: {settings.window_width} x {settings.window_height}")
        
        # 显示使用说明
        msg = QMessageBox()
        msg.setWindowTitle("全屏功能演示")
        msg.setIcon(QMessageBox.Icon.Information)
        
        if settings.fullscreen:
            msg.setText("全屏模式已启用！")
            msg.setInformativeText(
                "快捷键说明：\n"
                "• ESC - 退出全屏\n"
                "• F11 - 切换全屏模式\n\n"
                "登录成功后，主界面将以全屏模式显示。"
            )
        else:
            msg.setText("窗口模式")
            msg.setInformativeText(
                f"当前窗口尺寸：{settings.window_width} x {settings.window_height}\n\n"
                "要启用全屏模式，请修改 config/app_config.json 中的\n"
                "ui.fullscreen 设置为 true"
            )
        
        msg.exec()
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

def show_config_info():
    """显示配置信息"""
    print("\n⚙️  配置信息")
    print("=" * 50)
    
    try:
        import json
        
        # 读取配置文件
        config_file = "config/app_config.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print("📄 当前配置:")
            print(f"  应用名称: {config.get('app', {}).get('name', 'N/A')}")
            print(f"  全屏模式: {config.get('ui', {}).get('fullscreen', False)}")
            print(f"  窗口宽度: {config.get('ui', {}).get('window_width', 1200)}")
            print(f"  窗口高度: {config.get('ui', {}).get('window_height', 800)}")
            print(f"  主题: {config.get('ui', {}).get('theme', 'glassmorphism')}")
            
            print("\n🔧 修改全屏设置:")
            print("1. 编辑 config/app_config.json")
            print("2. 修改 ui.fullscreen 为 true 或 false")
            print("3. 重启应用程序")
            
            print("\n📝 配置示例:")
            print('"ui": {')
            print('  "fullscreen": true,    // 全屏模式')
            print('  "window_width": 1200,  // 窗口宽度')
            print('  "window_height": 800   // 窗口高度')
            print('}')
            
        else:
            print("❌ 配置文件不存在")
            
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")

def main():
    """主函数"""
    print("🚀 智慧食堂管理系统 - 全屏功能演示")
    print("=" * 60)
    
    # 显示配置信息
    show_config_info()
    
    # 询问是否启动演示
    try:
        choice = input("\n是否启动全屏演示？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是', '']:
            demo_fullscreen()
        else:
            print("演示已取消")
    except KeyboardInterrupt:
        print("\n演示已取消")
    except:
        # 如果无法获取输入，直接启动演示
        demo_fullscreen()

if __name__ == "__main__":
    main()
