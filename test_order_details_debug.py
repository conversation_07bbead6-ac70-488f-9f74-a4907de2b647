#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试订单详情调试功能
Test Order Details Debug Features
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit
from PyQt6.QtCore import QTimer
from ui.modules.order_module import OrderModule
from api.order_api import OrderAPI

class OrderDetailsDebugWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("订单详情调试测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置背景
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
            }
        """)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建测试按钮
        test_btn = QPushButton("测试订单详情调试功能")
        test_btn.setStyleSheet("""
            QPushButton {
                background: rgba(147, 51, 234, 0.3);
                color: white;
                border: 1px solid rgba(147, 51, 234, 0.5);
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
                margin: 10px;
            }
            QPushButton:hover {
                background: rgba(147, 51, 234, 0.5);
            }
        """)
        test_btn.clicked.connect(self.test_debug_features)
        layout.addWidget(test_btn)
        
        # 创建日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setStyleSheet("""
            QTextEdit {
                background: rgba(0, 0, 0, 0.3);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 10px;
                font-family: 'Consolas', monospace;
                font-size: 12px;
            }
        """)
        self.log_text.setMaximumHeight(200)
        layout.addWidget(self.log_text)
        
        # 创建订单模块
        self.order_module = OrderModule()
        
        # 创建并设置API（使用测试token）
        self.order_api = OrderAPI("https://st.pcylsoft.com:9006/st/steelyard/")
        # 注意：这里需要设置真实的token
        test_token = "your_real_token_here"  # 请替换为真实token
        self.order_api.set_access_token(test_token)
        self.order_module.set_api(self.order_api)
        
        layout.addWidget(self.order_module)
        
        print("🔧 订单详情调试功能测试")
        print("=" * 50)
        print("✅ 新增调试功能：")
        print("1. API请求详细日志")
        print("2. 数据字段检查")
        print("3. Token验证")
        print("4. 错误堆栈跟踪")
        print("5. 数据合并过程监控")
        print("=" * 50)
        
        # 设置测试数据
        self.setup_test_data()
        
        # 重定向print输出到日志区域
        self.redirect_print()
        
        # 10秒后自动关闭
        QTimer.singleShot(10000, self.close)
        
    def redirect_print(self):
        """重定向print输出到日志区域"""
        import sys
        
        class PrintRedirector:
            def __init__(self, text_widget):
                self.text_widget = text_widget
                
            def write(self, text):
                if text.strip():  # 只显示非空内容
                    self.text_widget.append(text.strip())
                    
            def flush(self):
                pass
        
        sys.stdout = PrintRedirector(self.log_text)
        
    def setup_test_data(self):
        """设置测试数据"""
        test_orders = [
            {
                'trade_no': 'CD22030712669',  # 使用真实的订单号
                'circulate_name': '测试供应商',
                'collect_time': '2022-03-07 23:10:20',
                'state': 0,
                'item_count': 2,
                'buy_quantity': '2',
                'deliver_quantity': '4.11',
                'goods': []
            }
        ]
        
        # 设置测试数据
        self.order_module.current_orders = test_orders
        self.order_module.populate_order_table(test_orders)
        print(f"📊 已加载 {len(test_orders)} 条测试订单")

    def test_debug_features(self):
        """测试调试功能"""
        print("\n🧪 开始测试调试功能...")
        
        # 检查API设置
        if self.order_module.api:
            print("✅ OrderModule API已设置")
            
            if hasattr(self.order_module.api, 'access_token'):
                token = self.order_module.api.access_token
                if token and token != "your_real_token_here":
                    print(f"✅ Token已设置: {token[:20]}...")
                else:
                    print("⚠️  请设置真实的token进行测试")
            else:
                print("❌ API缺少access_token属性")
        else:
            print("❌ OrderModule API未设置")
        
        # 模拟点击第一个订单
        if self.order_module.current_orders:
            first_order = self.order_module.current_orders[0]
            print(f"\n🔍 测试获取订单详情: {first_order['trade_no']}")
            
            try:
                # 直接调用详情显示方法，这会触发所有调试输出
                self.order_module.show_order_details(first_order)
                print("✅ 订单详情显示方法调用成功")
                
            except Exception as e:
                print(f"❌ 订单详情显示失败: {e}")
                import traceback
                traceback.print_exc()
                
        else:
            print("❌ 没有测试订单数据")
        
        print("\n📋 调试功能说明:")
        print("1. 🔄 API请求过程监控")
        print("2. 📊 响应数据结构检查")
        print("3. 🔍 关键字段值验证")
        print("4. 🛒 商品数据详情")
        print("5. 🔗 数据合并过程")
        print("6. ❌ 详细错误信息")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = OrderDetailsDebugWindow()
    window.show()
    
    print("⚠️  注意事项:")
    print("1. 请确保网络连接正常")
    print("2. 请设置有效的token")
    print("3. 请使用存在的订单ID")
    print("4. 观察控制台和日志区域的调试输出")
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
