#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API调试测试脚本
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_api_calls():
    """测试API调用"""
    try:
        print("🔍 导入API模块...")
        from api.stock_api import StockAPI
        from config.settings import Settings
        
        # 加载设置
        settings = Settings()
        print(f"🔍 API基础URL: {settings.api_base_url}")
        
        # 创建API实例
        api = StockAPI(settings.api_base_url)
        print(f"✅ StockAPI实例创建成功")
        
        # 检查认证状态
        print(f"🔍 认证状态: {api.is_authenticated()}")
        
        # 尝试设置一个测试token（如果有的话）
        # 注意：这里需要一个有效的token才能测试
        test_token = "your_test_token_here"  # 替换为实际的token
        if test_token != "your_test_token_here":
            api.set_access_token(test_token)
            print(f"🔧 设置测试token: {test_token[:10]}...")
            print(f"🔍 认证状态: {api.is_authenticated()}")
        else:
            print("⚠️ 没有提供测试token，无法测试认证API调用")
            return
        
        # 测试各个API调用
        print("\n" + "="*50)
        print("🧪 测试仓库列表API...")
        try:
            result = api.get_depot_list()
            print(f"📥 仓库列表结果: {result}")
            if result.get('code') == 200:
                print(f"✅ 仓库列表获取成功，数据量: {len(result.get('data', []))}")
            else:
                print(f"❌ 仓库列表获取失败: {result.get('msg')}")
        except Exception as e:
            print(f"❌ 仓库列表API调用异常: {e}")
        
        print("\n" + "="*50)
        print("🧪 测试项目列表API...")
        try:
            result = api.get_project_list()
            print(f"📥 项目列表结果: {result}")
            if result.get('code') == 200:
                print(f"✅ 项目列表获取成功，数据量: {len(result.get('data', []))}")
            else:
                print(f"❌ 项目列表获取失败: {result.get('msg')}")
        except Exception as e:
            print(f"❌ 项目列表API调用异常: {e}")
        
        print("\n" + "="*50)
        print("🧪 测试餐别列表API...")
        try:
            result = api.get_meal_time_list()
            print(f"📥 餐别列表结果: {result}")
            if result.get('code') == 200:
                print(f"✅ 餐别列表获取成功，数据量: {len(result.get('data', []))}")
            else:
                print(f"❌ 餐别列表获取失败: {result.get('msg')}")
        except Exception as e:
            print(f"❌ 餐别列表API调用异常: {e}")
        
        print("\n" + "="*50)
        print("🧪 测试库存列表API...")
        try:
            result = api.get_stock_list()
            print(f"📥 库存列表结果: {result}")
            if result.get('code') == 200:
                print(f"✅ 库存列表获取成功，数据量: {len(result.get('data', []))}")
            else:
                print(f"❌ 库存列表获取失败: {result.get('msg')}")
        except Exception as e:
            print(f"❌ 库存列表API调用异常: {e}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def test_mock_data():
    """测试模拟数据"""
    print("\n" + "="*50)
    print("🧪 测试模拟数据...")
    
    # 模拟API响应数据
    mock_depot_data = {
        "code": 200,
        "msg": "success",
        "data": [
            {"code": "S01", "name": "食堂仓库", "number": "01"},
            {"code": "S02", "name": "调料仓库", "number": "02"}
        ],
        "total": 2
    }
    
    mock_project_data = {
        "code": 200,
        "msg": "success", 
        "data": [
            {"code": "P01", "name": "早餐项目", "number": "01"},
            {"code": "P02", "name": "午餐项目", "number": "02"}
        ],
        "total": 2
    }
    
    print(f"📊 模拟仓库数据: {mock_depot_data}")
    print(f"📊 模拟项目数据: {mock_project_data}")
    
    # 测试数据解析
    depot_list = mock_depot_data.get('data', [])
    project_list = mock_project_data.get('data', [])
    
    print(f"✅ 解析出{len(depot_list)}个仓库")
    print(f"✅ 解析出{len(project_list)}个项目")

def main():
    """主函数"""
    print("🚀 开始API调试测试")
    print("=" * 50)
    
    # 测试模拟数据
    test_mock_data()
    
    # 测试实际API调用
    test_api_calls()
    
    print("\n" + "=" * 50)
    print("🎉 API调试测试完成")

if __name__ == "__main__":
    main()
