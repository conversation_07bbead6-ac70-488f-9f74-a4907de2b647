#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单功能演示脚本
Order Feature Demo Script
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_order_api():
    """演示订单API功能"""
    print("=" * 60)
    print("📦 订单管理功能演示")
    print("=" * 60)
    
    print("""
🎯 功能概述:
订单管理模块提供了完整的订单查询、搜索和管理功能，
支持按日期筛选、供应商搜索、订单详情查看等操作。

📋 主要功能:
""")
    
    # 演示API接口
    demo_api_interface()
    
    # 演示UI界面
    demo_ui_interface()
    
    # 演示数据格式
    demo_data_format()

def demo_api_interface():
    """演示API接口"""
    print("\n🔧 API接口演示:")
    print("-" * 40)
    
    print("""
1. 获取订单列表:
   GET https://st.pcylsoft.com:9006/st/steelyard/?op=order
   
   请求参数:
   - day (可选): 日期，格式 YYYY-MM-DD
   
   请求头:
   - Authorization: your_access_token
   - Content-Type: application/x-www-form-urlencoded

2. Python代码示例:
   ```python
   from api.order_api import OrderAPI
   
   # 创建API实例
   api = OrderAPI("https://st.pcylsoft.com:9006/st/steelyard/")
   api.set_access_token(your_token)
   
   # 获取所有订单
   result = api.get_orders()
   
   # 获取指定日期订单
   result = api.get_orders("2023-06-08")
   
   # 获取今日订单
   result = api.get_today_orders()
   
   # 搜索订单
   order = api.search_orders_by_trade_no("CD22030712669")
   orders = api.search_orders_by_supplier("测试供应商")
   
   # 获取统计信息
   stats = api.get_order_statistics()
   ```
""")

def demo_ui_interface():
    """演示UI界面"""
    print("\n🎨 用户界面演示:")
    print("-" * 40)
    
    print("""
订单管理界面包含以下组件:

📅 筛选区域:
   • 日期选择器 - 选择特定日期的订单
   • 供应商搜索框 - 按供应商名称筛选
   • 订单号搜索框 - 按订单号精确搜索
   • 快捷按钮 - 今日订单、所有订单、刷新

📋 订单列表:
   • 表格显示订单基本信息
   • 支持排序和选择
   • 状态颜色标识
   • 实时筛选显示

📄 订单详情:
   • 显示选中订单的完整信息
   • 包含商品详情列表
   • 格式化显示数量和单位
   • 滚动查看长内容

🔄 操作功能:
   • 加载进度显示
   • 错误提示处理
   • 状态栏信息
   • 响应式布局
""")

def demo_data_format():
    """演示数据格式"""
    print("\n📊 数据格式演示:")
    print("-" * 40)
    
    print("""
服务器返回的订单数据格式:

{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "trade_no": "CD22030712669",           // 订单号
            "item_count": 2,                      // 商品种类数量
            "receive_item_count": 0,              // 已收货商品种类数量
            "buy_quantity": "2",                  // 采购总数量
            "deliver_quantity": "4.11",           // 配送总数量
            "collect_time": "2022-03-07 23:10:20", // 下单时间
            "state": 0,                           // 订单状态
            "circulate_name": "测试供应商(勿用)",    // 供应商名称
            "count": 2,                           // 商品总数
            "goods": [                            // 商品详情列表
                {
                    "id": 194933,
                    "spec": null,                 // 规格
                    "code": "13010501",           // 商品编码
                    "name": "菠萝",               // 商品名称
                    "buy_unit": "市斤",           // 采购单位
                    "buy_quantity": "1",          // 采购数量
                    "stock_quantity": 3,          // 库存数量
                    "deliver_unit": "市斤",       // 配送单位
                    "deliver_quantity": "3.11",   // 配送数量
                    "receive_quantity": null,     // 收货数量
                    "stock_mode": 1,              // 库存模式
                    "receive_date": null,         // 收货日期
                    "path": null,                 // 图片路径
                    "batch": null                 // 批次
                }
            ]
        }
    ],
    "total": 6                                    // 订单总数
}

📋 状态说明:
• state = 0: 待处理 (黄色标识)
• state = 1: 处理中 (蓝色标识)
• state = 2: 已完成 (绿色标识)
• state = 3: 已取消 (红色标识)
""")

def demo_usage_scenarios():
    """演示使用场景"""
    print("\n🎯 使用场景演示:")
    print("-" * 40)
    
    scenarios = [
        {
            "title": "日常订单查看",
            "description": "查看今日或指定日期的订单情况",
            "steps": [
                "1. 点击'今日订单'按钮",
                "2. 查看订单列表和统计信息",
                "3. 点击具体订单查看详情"
            ]
        },
        {
            "title": "供应商订单管理",
            "description": "查看特定供应商的所有订单",
            "steps": [
                "1. 在供应商搜索框输入供应商名称",
                "2. 系统自动筛选相关订单",
                "3. 查看该供应商的订单统计"
            ]
        },
        {
            "title": "订单状态跟踪",
            "description": "跟踪订单处理进度",
            "steps": [
                "1. 在订单号搜索框输入订单号",
                "2. 查看订单当前状态",
                "3. 查看商品收货情况"
            ]
        },
        {
            "title": "数据统计分析",
            "description": "分析订单和采购数据",
            "steps": [
                "1. 选择特定日期范围",
                "2. 查看订单统计信息",
                "3. 分析供应商分布和商品类别"
            ]
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n场景 {i}: {scenario['title']}")
        print(f"描述: {scenario['description']}")
        print("操作步骤:")
        for step in scenario['steps']:
            print(f"   {step}")

def demo_integration_guide():
    """演示集成指南"""
    print("\n🔧 集成指南:")
    print("-" * 40)
    
    print("""
如何在现有系统中集成订单功能:

1. 导入必要模块:
   ```python
   from api.order_api import OrderAPI
   from ui.modules.order_module import OrderModule
   ```

2. 创建API实例:
   ```python
   api = OrderAPI("https://st.pcylsoft.com:9006/st/steelyard/")
   api.set_access_token(your_access_token)
   ```

3. 创建UI模块:
   ```python
   order_module = OrderModule()
   order_module.set_api(api)
   ```

4. 集成到主界面:
   ```python
   # 添加到标签页或堆叠窗口
   main_widget.addTab(order_module, "订单管理")
   ```

5. 处理权限和错误:
   ```python
   if not api.is_authenticated():
       # 处理未登录情况
       show_login_dialog()
   ```

📋 依赖要求:
• PyQt6 - 用户界面框架
• requests - HTTP请求库
• 有效的API访问令牌
• 网络连接

⚠️ 注意事项:
• 确保API地址正确
• 处理网络异常情况
• 实现适当的错误提示
• 考虑数据缓存策略
""")

def main():
    """主函数"""
    print("🚀 启动订单功能演示...")
    
    # 基本功能演示
    demo_order_api()
    
    # 使用场景演示
    demo_usage_scenarios()
    
    # 集成指南
    demo_integration_guide()
    
    print("\n" + "=" * 60)
    print("✅ 订单功能演示完成！")
    print("=" * 60)
    
    print("""
🎉 订单管理功能特点:

✨ 功能完整:
   • 支持多种查询方式
   • 提供详细的订单信息
   • 实时数据筛选
   • 统计分析功能

🎨 界面友好:
   • 直观的操作界面
   • 响应式布局设计
   • 状态颜色标识
   • 进度提示反馈

🔧 易于集成:
   • 模块化设计
   • 清晰的API接口
   • 完善的错误处理
   • 详细的文档说明

🚀 立即开始:
   1. 运行 python run.py 启动系统
   2. 登录获取访问令牌
   3. 点击"订单管理"开始使用
   4. 或运行 python test_order_api.py 进行测试
""")

if __name__ == "__main__":
    main()
