#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
Simple Test Script
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    print("测试导入模块...")
    
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow
        from PyQt6.QtCore import Qt
        print("✅ PyQt6 导入成功")
    except ImportError as e:
        print(f"❌ PyQt6 导入失败: {e}")
        return False
    
    try:
        import requests
        print("✅ requests 导入成功")
    except ImportError as e:
        print(f"❌ requests 导入失败: {e}")
        return False
    
    try:
        from config.settings import settings
        print("✅ 配置模块导入成功")
    except ImportError as e:
        print(f"❌ 配置模块导入失败: {e}")
        return False
    
    try:
        from ui.styles import GlassmorphismStyles
        print("✅ 样式模块导入成功")
    except ImportError as e:
        print(f"❌ 样式模块导入失败: {e}")
        return False
    
    try:
        from api.auth_api import AuthAPI
        print("✅ API模块导入成功")
    except ImportError as e:
        print(f"❌ API模块导入失败: {e}")
        return False
    
    return True

def test_simple_window():
    """测试简单窗口"""
    print("\n测试创建简单窗口...")
    
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel
        from PyQt6.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        # 创建简单窗口
        window = QMainWindow()
        window.setWindowTitle("测试窗口")
        window.setFixedSize(400, 300)
        
        # 添加标签
        label = QLabel("测试成功！")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        window.setCentralWidget(label)
        
        print("✅ 简单窗口创建成功")
        
        # 显示窗口（但不进入事件循环）
        window.show()
        
        # 立即关闭
        window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 窗口创建失败: {e}")
        return False

def test_login_window():
    """测试登录窗口"""
    print("\n测试登录窗口...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.login_window import LoginWindow
        
        app = QApplication(sys.argv)
        
        # 创建登录窗口
        login_window = LoginWindow()
        print("✅ 登录窗口创建成功")
        
        # 显示窗口（但不进入事件循环）
        login_window.show()
        
        # 立即关闭
        login_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 登录窗口创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("🧪 智慧食堂管理系统 - 简单测试")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败，请检查依赖包")
        return
    
    # 测试简单窗口
    if not test_simple_window():
        print("\n❌ 简单窗口测试失败")
        return
    
    # 测试登录窗口
    if not test_login_window():
        print("\n❌ 登录窗口测试失败")
        return
    
    print("\n✅ 所有测试通过！")
    print("可以尝试运行完整程序: python main.py")

if __name__ == "__main__":
    main()
