#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试串口状态
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_serial_ports():
    """检查可用的串口"""
    print("🔍 检查系统串口状态")
    print("=" * 40)
    
    try:
        import serial.tools.list_ports
        
        # 列出所有串口
        ports = serial.tools.list_ports.comports()
        
        if not ports:
            print("❌ 未发现任何串口设备")
            return []
        
        print(f"✅ 发现 {len(ports)} 个串口设备:")
        
        available_ports = []
        for port in ports:
            print(f"  📍 {port.device}: {port.description}")
            
            # 测试端口是否可用
            try:
                import serial
                test_serial = serial.Serial(port.device, 9600, timeout=0.1)
                test_serial.close()
                print(f"    ✅ 端口可用")
                available_ports.append(port.device)
            except Exception as e:
                print(f"    ❌ 端口被占用: {e}")
        
        return available_ports
        
    except Exception as e:
        print(f"❌ 检查串口失败: {e}")
        return []

def check_config_settings():
    """检查配置设置"""
    print("\n🔍 检查配置设置")
    print("=" * 40)
    
    try:
        from config.settings import settings
        
        print(f"📋 串口配置:")
        print(f"  端口: {settings.serial_port}")
        print(f"  波特率: {settings.serial_baudrate}")
        print(f"  超时: {settings.serial_timeout}")
        print(f"  自动检测: {settings.serial_auto_detect}")
        
        return {
            'port': settings.serial_port,
            'baudrate': settings.serial_baudrate,
            'timeout': settings.serial_timeout,
            'auto_detect': settings.serial_auto_detect
        }
        
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")
        return None

def test_serial_worker():
    """测试串口工作类"""
    print("\n🔍 测试串口工作类")
    print("=" * 40)
    
    try:
        from ui.modules.weight_submission_module import SerialWorker
        
        config = check_config_settings()
        if not config:
            print("❌ 无法获取配置，跳过测试")
            return
        
        print(f"🚀 尝试创建 SerialWorker 实例...")
        worker = SerialWorker(
            config['port'], 
            config['baudrate'], 
            config['timeout'], 
            config['auto_detect']
        )
        
        print(f"✅ SerialWorker 创建成功")
        print(f"  端口: {worker.port}")
        print(f"  波特率: {worker.baudrate}")
        print(f"  运行状态: {worker.running}")
        
        return True
        
    except Exception as e:
        print(f"❌ SerialWorker 测试失败: {e}")
        return False

def check_port_conflicts():
    """检查端口冲突"""
    print("\n🔍 检查端口冲突")
    print("=" * 40)
    
    config = check_config_settings()
    if not config:
        return
    
    port = config['port']
    
    try:
        import serial
        
        # 尝试连接两次，模拟冲突
        print(f"🔌 尝试第一次连接 {port}...")
        serial1 = serial.Serial(port, config['baudrate'], timeout=0.1)
        print(f"✅ 第一次连接成功")
        
        print(f"🔌 尝试第二次连接 {port}...")
        try:
            serial2 = serial.Serial(port, config['baudrate'], timeout=0.1)
            print(f"✅ 第二次连接也成功（意外）")
            serial2.close()
        except Exception as e:
            print(f"❌ 第二次连接失败（预期）: {e}")
        
        serial1.close()
        print(f"🔌 关闭第一次连接")
        
    except Exception as e:
        print(f"❌ 端口冲突测试失败: {e}")

def main():
    """主函数"""
    print("🚀 串口状态调试工具")
    print("=" * 50)
    
    # 检查系统串口
    available_ports = check_serial_ports()
    
    # 检查配置
    config = check_config_settings()
    
    # 测试串口工作类
    worker_ok = test_serial_worker()
    
    # 检查端口冲突
    if available_ports and config:
        check_port_conflicts()
    
    print("\n" + "=" * 50)
    print("🎉 调试总结")
    
    if available_ports:
        print(f"✅ 可用串口: {', '.join(available_ports)}")
    else:
        print("❌ 无可用串口")
    
    if config:
        print(f"✅ 配置端口: {config['port']}")
        if config['port'] in available_ports:
            print("✅ 配置端口可用")
        else:
            print("❌ 配置端口不可用或被占用")
    
    if worker_ok:
        print("✅ SerialWorker 类正常")
    else:
        print("❌ SerialWorker 类有问题")
    
    print("\n📋 建议:")
    if not available_ports:
        print("1. 检查重量秤设备是否连接")
        print("2. 检查USB驱动是否安装")
        print("3. 重新插拔设备")
    elif config and config['port'] not in available_ports:
        print(f"1. 配置的端口 {config['port']} 不可用")
        print(f"2. 建议修改配置为可用端口: {', '.join(available_ports)}")
    else:
        print("1. 串口硬件正常")
        print("2. 检查是否有其他程序占用端口")
        print("3. 确保只有一个模块连接串口")

if __name__ == "__main__":
    main()
