@echo off
chcp 65001 >nul
title 智慧食堂管理系统

echo ========================================
echo 智慧食堂管理系统 v1.0.0
echo ========================================
echo.
echo 正在启动程序，请稍候...
echo.

REM 检查可执行文件是否存在
if not exist "智慧食堂管理系统.exe" (
    echo ❌ 错误：找不到程序文件 "智慧食堂管理系统.exe"
    echo 请确保此批处理文件与程序文件在同一目录下
    echo.
    pause
    exit /b 1
)

REM 启动程序
start "" "智慧食堂管理系统.exe"

REM 等待程序启动
timeout /t 2 /nobreak >nul

echo ✅ 程序已启动！
echo.
echo 如果程序没有正常显示，请：
echo 1. 检查系统是否为 Windows 7 及以上版本
echo 2. 确保有足够的内存空间（建议4GB以上）
echo 3. 检查网络连接是否正常
echo.
echo 按任意键关闭此窗口...
pause >nul
