#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输入框修复测试
Input Fix Test
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_input_display():
    """测试输入框显示"""
    print("=" * 60)
    print("🔧 输入框显示修复测试")
    print("=" * 60)
    
    try:
        from PyQt6.QtWidgets import QApplication, QLineEdit, QWidget, QVBoxLayout, QLabel
        from ui.styles import GlassmorphismStyles
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建测试窗口
        window = QWidget()
        window.setWindowTitle("输入框显示修复测试")
        window.setFixedSize(450, 350)
        
        layout = QVBoxLayout(window)
        layout.setSpacing(15)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title_label = QLabel("输入框显示修复测试")
        title_label.setStyleSheet("""
        QLabel {
            color: white;
            font-size: 18px;
            font-weight: 700;
            background: transparent;
            border: none;
            padding: 10px;
            text-align: center;
        }
        """)
        layout.addWidget(title_label)
        
        # 用户名输入框
        username_label = QLabel("用户名:")
        username_label.setStyleSheet("color: white; font-size: 14px; font-weight: 500; margin-bottom: 5px;")
        
        username_input = QLineEdit()
        username_input.setPlaceholderText("请输入用户名")
        username_input.setFixedHeight(48)  # 与修复后一致
        username_input.setStyleSheet(GlassmorphismStyles.get_input_style())
        username_input.setText("测试用户名")  # 添加测试文字
        
        # 密码输入框
        password_label = QLabel("密码:")
        password_label.setStyleSheet("color: white; font-size: 14px; font-weight: 500; margin-bottom: 5px;")
        
        password_input = QLineEdit()
        password_input.setPlaceholderText("请输入密码")
        password_input.setFixedHeight(48)  # 与修复后一致
        password_input.setStyleSheet(GlassmorphismStyles.get_input_style())
        password_input.setText("测试密码123")  # 添加测试文字
        
        # 添加到布局
        layout.addWidget(username_label)
        layout.addWidget(username_input)
        layout.addWidget(password_label)
        layout.addWidget(password_input)
        
        # 测试说明
        info_label = QLabel("""
✅ 修复内容:
• 输入框高度: 48px (固定高度)
• 内边距: 12px 16px (适中间距)
• 圆角: 16px (统一样式)
• 文字颜色: 白色
• 字体大小: 16px

🔍 检查要点:
• 输入框应该完整显示
• 文字应该清晰可见
• 圆角应该统一
• 点击时有紫色边框效果
        """)
        info_label.setStyleSheet("""
        QLabel {
            color: rgba(255, 255, 255, 0.9);
            font-size: 11px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 12px;
            line-height: 1.4;
        }
        """)
        layout.addWidget(info_label)
        
        # 设置窗口背景
        window.setStyleSheet("""
        QWidget {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(30, 30, 60, 1),
                stop:0.5 rgba(60, 30, 90, 1),
                stop:1 rgba(90, 60, 120, 1));
        }
        """)
        
        print("✅ 测试窗口创建成功")
        print("📋 修复参数:")
        print("  • 输入框高度: 48px")
        print("  • 内边距: 12px 16px")
        print("  • 圆角半径: 16px")
        print("  • 字体大小: 16px")
        
        print("\n💡 预期效果:")
        print("  • 输入框完整显示，不被截断")
        print("  • 文字清晰可见")
        print("  • 两个输入框样式一致")
        print("  • 焦点时有紫色边框")
        
        window.show()
        
        print("\n🖼️ 测试窗口已显示")
        print("请检查输入框是否正常显示")
        print("按Ctrl+C退出测试")
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_current_settings():
    """显示当前设置"""
    print("\n" + "=" * 60)
    print("⚙️ 当前输入框设置")
    print("=" * 60)
    
    try:
        from ui.styles import GlassmorphismStyles
        
        style = GlassmorphismStyles.get_input_style()
        print("🎨 当前样式:")
        print(style)
        
        print("\n📏 关键参数:")
        print("  • height: 48px (固定高度)")
        print("  • padding: 12px 16px (内边距)")
        print("  • border-radius: 16px (圆角)")
        print("  • font-size: 16px (字体大小)")
        print("  • color: white (文字颜色)")
        
    except Exception as e:
        print(f"❌ 获取设置失败: {e}")

def main():
    """主函数"""
    print("🍽️ 智慧食堂管理系统 - 输入框显示修复")
    
    # 显示当前设置
    show_current_settings()
    
    print("\n" + "=" * 60)
    print("🚀 启动显示测试")
    print("=" * 60)
    
    # 运行测试
    result = test_input_display()
    
    if result:
        print("\n✅ 显示测试完成")
        print("如果测试正常，现在可以运行主程序:")
        print("  python main.py")
    else:
        print("\n❌ 显示测试失败")

if __name__ == "__main__":
    main()
