# 登录逻辑修改说明

## 问题描述

原来的登录逻辑存在以下问题：
1. **首次打开**：程序启动时会检查token是否有效，如果有效就直接跳转主界面，如果无效就显示登录界面
2. **退出登录后**：主窗口的退出登录只是清除了`self.access_token = None`，但没有清除`auth_manager`中的认证信息，所以跳转到登录界面后仍然会检测到token并尝试验证

**期望行为**：
- 首次打开登录界面时，如果token未过期应该直接跳转到主界面
- 点击退出登录后，跳转到登录界面时应该跳过token检测，需要用户手动登录新账号

## 修改内容

### 1. 修改 `ui/main_window.py` 中的 `logout` 方法

**修改前**：
```python
def logout(self):
    """退出登录"""
    from PyQt6.QtWidgets import QMessageBox

    reply = QMessageBox.question(
        self, "确认退出", "确定要退出登录吗？",
        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        QMessageBox.StandardButton.No
    )

    if reply == QMessageBox.StandardButton.Yes:
        # 清除登录信息
        self.access_token = None

        # 关闭主窗口
        self.close()

        # 重新显示登录窗口
        from .login_window import LoginWindow
        self.login_window = LoginWindow()
        self.login_window.show()
```

**修改后**：
```python
def logout(self):
    """退出登录"""
    from PyQt6.QtWidgets import QMessageBox
    from PyQt6.QtCore import QTimer
    from utils.auth_manager import auth_manager

    reply = QMessageBox.question(
        self, "确认退出", "确定要退出登录吗？",
        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        QMessageBox.StandardButton.No
    )

    if reply == QMessageBox.StandardButton.Yes:
        # 清除登录信息
        self.access_token = None

        # 清除认证管理器中的认证信息
        auth_manager.clear_current_auth()

        # 先创建登录窗口，跳过token检查
        from .login_window import LoginWindow
        self.login_window = LoginWindow(skip_token_check=True)
        self.login_window.show()

        # 延迟关闭主窗口，确保登录窗口已经完全显示
        QTimer.singleShot(100, self._delayed_close)

def _delayed_close(self):
    """延迟关闭主窗口"""
    self.close()
```

**主要变化**：
1. 导入了 `auth_manager` 和 `QTimer`
2. 添加了 `auth_manager.clear_current_auth()` 来清除认证管理器中的认证信息
3. 先创建LoginWindow再关闭主窗口，避免应用程序意外退出
4. 使用 `QTimer.singleShot(100, self._delayed_close)` 延迟关闭主窗口
5. 创建LoginWindow时传递了 `skip_token_check=True` 参数
6. 添加了 `_delayed_close()` 辅助方法

### 2. 修改 `ui/login_window.py` 中的 `LoginWindow` 类

**修改前**：
```python
def __init__(self):
    super().__init__()
    self.setWindowTitle(f"{settings.app_name} - 登录")
    # ... 其他初始化代码 ...
    
    # 检查自动登录
    QTimer.singleShot(500, self.check_auto_login)
```

**修改后**：
```python
def __init__(self, skip_token_check=False):
    super().__init__()
    self.skip_token_check = skip_token_check  # 是否跳过token检查
    self.setWindowTitle(f"{settings.app_name} - 登录")
    # ... 其他初始化代码 ...
    
    # 检查自动登录（如果不跳过token检查）
    if not self.skip_token_check:
        QTimer.singleShot(500, self.check_auto_login)
    else:
        # 跳过token检查，直接加载历史账号
        QTimer.singleShot(100, self.load_username_history)
```

**主要变化**：
1. 构造函数添加了 `skip_token_check=False` 参数
2. 添加了 `self.skip_token_check` 实例变量
3. 根据 `skip_token_check` 的值决定是否执行token检查
4. 如果跳过token检查，直接调用 `load_username_history()` 加载历史账号

## 功能说明

修改后的登录逻辑行为如下：

### 首次启动程序（或程序启动）
1. 程序启动时调用 `LoginWindow()` （不传递参数，默认 `skip_token_check=False`）
2. 执行 `check_auto_login()` 方法
3. 如果检测到有效token，自动跳转到主界面
4. 如果token无效或不存在，显示登录界面供用户输入

### 退出登录后
1. 用户点击主界面的"退出登录"按钮
2. 确认后执行以下操作：
   - 清除主窗口的 `access_token`
   - 调用 `auth_manager.clear_current_auth()` 清除认证管理器中的认证信息
   - 关闭主窗口
   - 创建新的登录窗口：`LoginWindow(skip_token_check=True)`
3. 新的登录窗口跳过token检查，直接加载历史账号到用户名输入框
4. 用户需要手动输入密码进行登录

## 兼容性

- 所有现有的 `LoginWindow()` 调用（如 `main.py` 和 `start_fixed.py` 中的）都不需要修改
- 新增的 `skip_token_check` 参数有默认值 `False`，保持向后兼容
- 不影响现有的token验证和自动登录逻辑

## 测试建议

1. **首次启动测试**：
   - 清除所有认证信息，启动程序，应该显示登录界面
   - 登录成功后，重启程序，应该自动跳转到主界面（如果token未过期）

2. **退出登录测试**：
   - 登录成功进入主界面
   - 点击"退出登录"按钮
   - 确认退出后，应该显示登录界面，且不会自动验证token
   - 需要手动输入密码才能登录

3. **Token过期测试**：
   - 等待token过期或手动修改token过期时间
   - 重启程序，应该显示登录界面并提示需要重新登录

## 错误修复

### 问题：退出登录时系统报错 "进程已结束，退出代码为 -1073740791 (0xC0000409)"

**原因分析**：
- 错误代码 `0xC0000409` 表示堆栈缓冲区溢出或内存访问违规
- 问题出现在窗口关闭和重新创建的过程中
- 当主窗口关闭后，如果没有其他窗口保持应用程序运行，应用程序可能会意外退出

**解决方案**：
1. **调整窗口创建和关闭顺序**：先创建登录窗口，再关闭主窗口
2. **使用延迟关闭**：使用 `QTimer.singleShot(100, self._delayed_close)` 延迟关闭主窗口
3. **添加窗口关闭事件处理**：为MainWindow和LoginWindow添加 `closeEvent` 方法
4. **确保应用程序正确退出**：在适当的时候调用 `QApplication.quit()`

### 修改的关键点

1. **MainWindow.logout()** 方法：
   ```python
   # 先创建登录窗口
   self.login_window = LoginWindow(skip_token_check=True)
   self.login_window.show()

   # 延迟关闭主窗口
   QTimer.singleShot(100, self._delayed_close)
   ```

2. **添加closeEvent处理**：
   ```python
   def closeEvent(self, event):
       """窗口关闭事件"""
       # 适当的退出逻辑
       event.accept()
   ```

这些修改确保了窗口切换过程的稳定性，避免了内存访问违规和应用程序意外退出的问题。

## 其他修复

### 问题：QSizePolicy属性错误 "QSizePolicy' object has no attribute 'Fixed'"

**原因分析**：
- PyQt6中QSizePolicy的枚举值访问方式发生了变化
- 旧版本使用 `QSizePolicy.Fixed`
- PyQt6需要使用 `QSizePolicy.Policy.Fixed`

**解决方案**：
修改 `ui/login_window.py` 中的QSizePolicy使用：

```python
# 修改前
self.username_input.setSizePolicy(
    self.username_input.sizePolicy().horizontalPolicy(),
    self.username_input.sizePolicy().Fixed
)

# 修改后
from PyQt6.QtWidgets import QSizePolicy
self.username_input.setSizePolicy(
    QSizePolicy.Policy.Expanding,
    QSizePolicy.Policy.Fixed
)
```

### 问题：UTF-8编码错误

**解决方案**：
修改 `main.py` 中的异常处理，添加try-catch保护：

```python
# 修改前
input("按回车键退出...")

# 修改后
try:
    input("按回车键退出...")
except:
    pass
```

### 问题：登录成功后程序直接退出

**原因分析**：
- 登录成功后调用 `open_main_window` 方法
- 该方法会关闭登录窗口 `self.close()`
- 关闭登录窗口触发 `closeEvent` 事件
- `closeEvent` 中调用 `QApplication.quit()` 导致程序退出

**解决方案**：
修改 `ui/login_window.py` 中的窗口跳转逻辑：

```python
def open_main_window(self, access_token: str):
    """打开主界面"""
    from .main_window import MainWindow

    # 标记正在跳转到主界面，避免closeEvent退出应用程序
    self.is_opening_main_window = True

    # 创建主界面
    self.main_window = MainWindow(access_token)
    self.main_window.show()

    # 关闭登录窗口
    self.close()

def closeEvent(self, event):
    """窗口关闭事件"""
    # 如果是跳转到主界面，不退出应用程序
    if hasattr(self, 'is_opening_main_window') and self.is_opening_main_window:
        event.accept()
        return

    # 如果没有主窗口在运行，退出应用程序
    if not hasattr(self, 'main_window') or not self.main_window or not self.main_window.isVisible():
        from PyQt6.QtWidgets import QApplication
        QApplication.quit()
    event.accept()
```
