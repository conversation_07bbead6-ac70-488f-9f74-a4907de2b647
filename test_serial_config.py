#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口配置功能测试脚本
Serial Configuration Test Script
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_serial_config_dialog():
    """测试串口配置对话框"""
    print("🧪 测试串口配置对话框")
    print("=" * 50)
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.dialogs.serial_config_dialog import SerialConfigDialog
        
        app = QApplication(sys.argv)
        
        # 创建对话框
        dialog = SerialConfigDialog()
        
        print("✅ 串口配置对话框创建成功")
        print("📝 功能说明:")
        print("   1. 可以选择串口端口")
        print("   2. 可以设置波特率")
        print("   3. 可以设置超时时间")
        print("   4. 支持自动检测重量秤设备")
        print("   5. 可以测试串口连接")
        print("   6. 可以保存配置到文件")
        
        # 显示对话框
        dialog.show()
        
        return app.exec()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return 1
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return 1

def test_serial_settings():
    """测试串口设置"""
    print("\n🔧 测试串口设置")
    print("=" * 30)
    
    try:
        from config.settings import settings
        
        print(f"当前串口配置:")
        print(f"  端口: {settings.serial_port}")
        print(f"  波特率: {settings.serial_baudrate}")
        print(f"  超时时间: {settings.serial_timeout}")
        print(f"  自动检测: {settings.serial_auto_detect}")
        
        # 测试设置修改
        print("\n测试配置修改...")
        original_port = settings.serial_port
        
        settings.set('serial.port', 'COM5')
        print(f"修改端口为: {settings.serial_port}")
        
        # 恢复原设置
        settings.set('serial.port', original_port)
        print(f"恢复端口为: {settings.serial_port}")
        
        print("✅ 串口设置测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 串口设置测试失败: {e}")
        return False

def test_serial_detection():
    """测试串口检测功能"""
    print("\n🔍 测试串口检测功能")
    print("=" * 30)
    
    try:
        import serial.tools.list_ports
        
        # 获取所有串口
        ports = list(serial.tools.list_ports.comports())
        print(f"发现 {len(ports)} 个串口:")
        
        for port in ports:
            print(f"  - {port.device}: {port.description}")
        
        if not ports:
            print("  (未发现任何串口)")
        
        print("✅ 串口检测功能正常")
        return True
        
    except ImportError:
        print("❌ pyserial库未安装，无法检测串口")
        print("请运行: pip install pyserial")
        return False
    except Exception as e:
        print(f"❌ 串口检测失败: {e}")
        return False

def test_weight_submission_integration():
    """测试重量提交模块集成"""
    print("\n🔗 测试重量提交模块集成")
    print("=" * 30)
    
    try:
        from ui.modules.weight_submission_module import SerialWorker
        from config.settings import settings
        
        # 创建串口工作线程（不启动）
        worker = SerialWorker(
            port=settings.serial_port,
            baudrate=settings.serial_baudrate,
            timeout=settings.serial_timeout,
            auto_detect=settings.serial_auto_detect
        )
        
        print(f"串口工作线程配置:")
        print(f"  端口: {worker.port}")
        print(f"  波特率: {worker.baudrate}")
        print(f"  超时时间: {worker.timeout}")
        print(f"  自动检测: {worker.auto_detect}")
        
        print("✅ 重量提交模块集成测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 重量提交模块集成测试失败: {e}")
        return False

def test_main_window_integration():
    """测试主窗口集成"""
    print("\n🏠 测试主窗口集成")
    print("=" * 30)
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.main_window import MainWindow
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        window = MainWindow()
        
        # 检查是否有设置按钮的方法
        if hasattr(window, 'open_settings'):
            print("✅ 主窗口包含设置功能")
        else:
            print("❌ 主窗口缺少设置功能")
            return False
        
        if hasattr(window, 'open_serial_config'):
            print("✅ 主窗口包含串口配置功能")
        else:
            print("❌ 主窗口缺少串口配置功能")
            return False
        
        print("✅ 主窗口集成测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 主窗口集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 串口配置功能测试")
    print("=" * 50)
    
    # 运行各项测试
    tests = [
        ("串口设置", test_serial_settings),
        ("串口检测", test_serial_detection),
        ("重量提交模块集成", test_weight_submission_integration),
        ("主窗口集成", test_main_window_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n📊 测试结果汇总")
    print("=" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    # 如果所有测试都通过，启动GUI测试
    if passed == len(results):
        print("\n🎉 所有测试通过！启动GUI测试...")
        return test_serial_config_dialog()
    else:
        print("\n⚠️  部分测试失败，请检查配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())
