#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表格行高测试 - 验证订单列表行高和序号显示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTableWidget, QTableWidgetItem, QHeaderView
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

def test_table_height():
    """测试表格行高设置"""
    app = QApplication(sys.argv)
    
    window = QMainWindow()
    window.setWindowTitle("表格行高测试")
    window.setGeometry(100, 100, 800, 600)
    
    # 设置背景
    window.setStyleSheet("""
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #667eea, stop:1 #764ba2);
        }
    """)
    
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 创建测试表格
    table = QTableWidget()
    table.setColumnCount(4)
    table.setHorizontalHeaderLabels(["订单号", "供应商", "状态", "数量"])
    
    # 设置表格样式
    table.setStyleSheet("""
        QTableWidget {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            gridline-color: rgba(255, 255, 255, 0.1);
            selection-background-color: rgba(147, 51, 234, 0.3);
            selection-color: white;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }
        QTableWidget::item {
            padding: 12px 8px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: transparent;
            min-height: 25px;
        }
        QTableWidget::item:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        QTableWidget::item:selected {
            background: rgba(147, 51, 234, 0.3);
            color: white;
        }
        QHeaderView::section {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            padding: 12px;
            border: none;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            font-weight: 600;
            font-size: 14px;
        }
        QHeaderView::section:hover {
            background: rgba(255, 255, 255, 0.15);
        }
        QHeaderView::section:vertical {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            padding: 8px;
            border: none;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-weight: 600;
            font-size: 13px;
            text-align: center;
            min-width: 40px;
        }
        QHeaderView::section:vertical:hover {
            background: rgba(255, 255, 255, 0.15);
        }
    """)
    
    # 设置行高
    table.verticalHeader().setDefaultSectionSize(45)
    
    # 设置垂直表头（序号）样式
    vertical_header = table.verticalHeader()
    vertical_header.setVisible(True)
    vertical_header.setDefaultSectionSize(45)
    vertical_header.setMinimumSectionSize(45)
    vertical_header.setSectionResizeMode(QHeaderView.ResizeMode.Fixed)
    
    # 添加测试数据
    test_data = [
        ("ORDER-001", "蔬菜供应商", "待处理", "25.5"),
        ("ORDER-002", "肉类供应商", "处理中", "18.0"),
        ("ORDER-003", "海鲜供应商", "已完成", "12.5"),
        ("ORDER-004", "调料供应商", "待处理", "8.5"),
        ("ORDER-005", "粮油供应商", "处理中", "50.0"),
        ("ORDER-006", "饮料供应商", "已取消", "30.0"),
        ("ORDER-007", "冷冻供应商", "已完成", "22.5"),
        ("ORDER-008", "干货供应商", "待处理", "15.8"),
        ("ORDER-009", "水果供应商", "处理中", "35.2"),
        ("ORDER-010", "奶制品供应商", "已完成", "28.0"),
    ]
    
    table.setRowCount(len(test_data))
    
    # 填充数据并设置行高
    for row, (order_no, supplier, status, quantity) in enumerate(test_data):
        # 确保每行都有正确的高度
        table.setRowHeight(row, 45)
        
        # 订单号
        item1 = QTableWidgetItem(order_no)
        item1.setTextAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        table.setItem(row, 0, item1)
        
        # 供应商
        item2 = QTableWidgetItem(supplier)
        item2.setTextAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        table.setItem(row, 1, item2)
        
        # 状态
        item3 = QTableWidgetItem(status)
        item3.setTextAlignment(Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignVCenter)
        table.setItem(row, 2, item3)
        
        # 数量
        item4 = QTableWidgetItem(quantity)
        item4.setTextAlignment(Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignVCenter)
        table.setItem(row, 3, item4)
    
    layout.addWidget(table)
    
    print("🧪 表格行高测试")
    print("=" * 40)
    print("✅ 测试配置：")
    print(f"- 默认行高：45px")
    print(f"- 垂直表头：显示")
    print(f"- 序号列宽：40px最小宽度")
    print(f"- 测试数据：{len(test_data)} 行")
    print("=" * 40)
    print("🔍 检查要点：")
    print("- 左侧序号列（1,2,3...10）是否完整显示")
    print("- 每行高度是否为45px")
    print("- 文字是否垂直居中对齐")
    print("- 序号列是否有足够宽度")
    print("=" * 40)
    
    window.show()
    
    # 3秒后自动关闭
    from PyQt6.QtCore import QTimer
    QTimer.singleShot(3000, window.close)
    
    sys.exit(app.exec())

if __name__ == '__main__':
    test_table_height()
