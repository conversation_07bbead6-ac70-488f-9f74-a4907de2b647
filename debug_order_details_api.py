#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试订单详情API请求
Debug Order Details API Request
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
from api.order_api import OrderAPI

def debug_order_details_api():
    """调试订单详情API"""
    print("🔍 调试订单详情API请求")
    print("=" * 60)
    
    # 测试用的token（需要替换为实际的token）
    test_token = "your_actual_token_here"  # 请替换为实际的token
    test_order_id = "CD22030712669"  # 测试订单ID
    
    print(f"📋 测试参数:")
    print(f"  - 订单ID: {test_order_id}")
    print(f"  - Token: {test_token[:20]}..." if len(test_token) > 20 else f"  - Token: {test_token}")
    print(f"  - Flag: 1 (已验收)")
    
    # 方法1: 直接使用requests测试
    print("\n🔧 方法1: 直接requests测试")
    print("-" * 40)
    
    url = "https://st.pcylsoft.com:9006/st/steelyard/?op=order_item"
    headers = {
        'Authorization': test_token,
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    data = {
        'order_id': test_order_id,
        'flag': 1
    }
    
    try:
        print(f"📡 请求URL: {url}")
        print(f"📋 请求头: {headers}")
        print(f"📦 请求数据: {data}")
        
        response = requests.post(url, headers=headers, data=data, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ 响应数据: {result}")
                
                if result.get('code') == 200:
                    data_obj = result.get('data', {})
                    print(f"\n📦 订单详情数据:")
                    print(f"  - 订单号: {data_obj.get('trade_no', 'N/A')}")
                    print(f"  - 商品种类: {data_obj.get('item_count', 0)}")
                    print(f"  - 已收货种类: {data_obj.get('receive_item_count', 0)}")
                    print(f"  - 采购总量: {data_obj.get('buy_quantity', 'N/A')}")
                    print(f"  - 配送总量: {data_obj.get('deliver_quantity', 'N/A')}")
                    print(f"  - 商品总数: {data_obj.get('count', 0)}")
                    
                    goods = data_obj.get('goods', [])
                    print(f"  - 商品详情数量: {len(goods)}")
                    
                    if goods:
                        print(f"\n🛒 商品详情 (前3个):")
                        for i, good in enumerate(goods[:3]):
                            print(f"    商品{i+1}:")
                            print(f"      - 名称: {good.get('name', 'N/A')}")
                            print(f"      - 编码: {good.get('code', 'N/A')}")
                            print(f"      - 采购数量: {good.get('buy_quantity', 'N/A')}")
                            print(f"      - 配送数量: {good.get('deliver_quantity', 'N/A')}")
                            print(f"      - 收货数量: {good.get('receive_quantity', 'N/A')}")
                    
                else:
                    print(f"❌ API返回错误: {result.get('msg', '未知错误')}")
                    
            except Exception as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"📄 原始响应: {response.text[:500]}...")
                
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"📄 响应内容: {response.text[:500]}...")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 方法2: 使用OrderAPI类测试
    print("\n🔧 方法2: OrderAPI类测试")
    print("-" * 40)
    
    try:
        # 创建API实例
        api = OrderAPI("https://st.pcylsoft.com:9006/st/steelyard/")
        api.set_access_token(test_token)
        
        print(f"✅ OrderAPI实例创建成功")
        print(f"📋 Token设置: {api.access_token[:20]}..." if len(api.access_token) > 20 else f"📋 Token设置: {api.access_token}")
        
        # 检查session headers
        print(f"📋 Session Headers: {dict(api.session.headers)}")
        
        # 调用get_order_details方法
        result = api.get_order_details(test_order_id, flag=1)
        
        print(f"📊 API调用结果: {result}")
        
        if result.get('code') == 200:
            print("✅ API调用成功")
            data_obj = result.get('data', {})
            
            # 检查关键字段
            key_fields = ['trade_no', 'item_count', 'receive_item_count', 
                         'buy_quantity', 'deliver_quantity', 'count', 'goods']
            
            print(f"\n📋 关键字段检查:")
            for field in key_fields:
                value = data_obj.get(field, 'MISSING')
                print(f"  - {field}: {value}")
                
        else:
            print(f"❌ API调用失败: {result.get('msg', '未知错误')}")
            
    except Exception as e:
        print(f"❌ OrderAPI测试异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 调试总结:")
    print("1. 检查token是否有效")
    print("2. 检查订单ID是否存在")
    print("3. 检查API响应数据结构")
    print("4. 检查Authorization header设置")
    print("5. 检查请求参数格式")
    
    print("\n💡 可能的问题:")
    print("- Token过期或无效")
    print("- 订单ID不存在")
    print("- API接口变更")
    print("- 网络连接问题")
    print("- 请求参数错误")

def get_sample_token():
    """获取示例token（需要实际登录获取）"""
    print("\n🔑 获取Token示例:")
    print("1. 运行主程序并登录")
    print("2. 在登录成功后，token会保存在session中")
    print("3. 可以从main_window.access_token获取")
    print("4. 或者从API实例的access_token属性获取")

if __name__ == '__main__':
    print("⚠️  注意: 请先设置有效的token和订单ID")
    print("可以从已登录的程序中获取这些信息")
    print()
    
    # 如果有实际的token，可以取消注释下面的行进行测试
    # debug_order_details_api()
    
    get_sample_token()
