#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试版主程序
Debug Main Program
"""

import sys
import os
import traceback

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_step(step_name, func):
    """调试步骤"""
    print(f"🔍 {step_name}...")
    try:
        result = func()
        print(f"✅ {step_name} 成功")
        return result
    except Exception as e:
        print(f"❌ {step_name} 失败: {e}")
        traceback.print_exc()
        return None

def test_pyqt_import():
    """测试PyQt6导入"""
    from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    return True

def test_create_app():
    """测试创建应用"""
    from PyQt6.QtWidgets import QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("测试应用")
    return app

def test_import_modules():
    """测试导入项目模块"""
    from ui.styles import GlassmorphismStyles
    from config.settings import settings
    from api.auth_api import AuthAPI
    return True

def test_create_login_window():
    """测试创建登录窗口"""
    from ui.login_window import LoginWindow
    window = LoginWindow()
    return window

def main():
    """主程序"""
    print("=" * 60)
    print("🐛 智慧食堂管理系统 - 调试模式")
    print("=" * 60)
    
    # 步骤1: 测试PyQt6导入
    if not debug_step("导入PyQt6", test_pyqt_import):
        return
    
    # 步骤2: 测试创建应用
    app = debug_step("创建QApplication", test_create_app)
    if not app:
        return
    
    # 步骤3: 测试导入项目模块
    if not debug_step("导入项目模块", test_import_modules):
        print("⚠️ 项目模块导入失败，但继续尝试...")
    
    # 步骤4: 测试创建登录窗口
    window = debug_step("创建登录窗口", test_create_login_window)
    if not window:
        print("❌ 无法创建登录窗口，程序退出")
        return
    
    print("🎉 所有测试通过！显示登录窗口...")
    
    # 显示窗口
    window.show()
    
    print("📱 登录窗口已显示，进入事件循环...")
    
    # 运行应用程序
    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("\n👋 用户中断程序")
    except Exception as e:
        print(f"❌ 程序运行错误: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
