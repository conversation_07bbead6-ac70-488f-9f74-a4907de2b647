# 入库模块API文档

## 概述

入库模块提供了完整的货品入库管理功能，包括货品查询、供应商管理、入库单创建和提交等功能。

## API基础信息

- **域名**: `https://st.pcylsoft.com:9006`
- **认证方式**: Token认证，放在Header的`Authorization`字段
- **请求格式**: POST请求，支持`application/x-www-form-urlencoded`和`application/json`

## 主要功能

### 1. 货品接口 (stock_products)

获取所有可入库的货品列表。

**接口地址**: `https://st.pcylsoft.com:9006/?op=stock_products`

**返回字段**:
- `code`: 货品代码
- `name`: 货品名称  
- `unit`: 货品单位

### 2. 供应商接口 (stock_companys)

获取所有供应商列表。

**接口地址**: `https://st.pcylsoft.com:9006/?op=stock_companys`

**返回字段**:
- `code`: 供应商代码
- `name`: 供应商名称

### 3. 入库单号获取接口 (stockin_index)

获取新的入库单号。

**接口地址**: `https://st.pcylsoft.com:9006/?op=stockin_index`

**返回字段**:
- `msg`: 入库单号

### 4. 入库提交接口 (stockin_submit)

提交入库申请。

**接口地址**: `https://st.pcylsoft.com:9006/?op=stockin_submit`

**提交参数**:
- `code`: 入库单号（从上个接口获取）
- `datetime`: 入库时间（YYYY-MM-DD格式）
- `depotcode`: 仓库代码
- `companycode`: 供应商代码
- `details`: 入库明细列表

### 5. 入库明细字段

每个入库明细项包含以下字段：
- `code`: 商品代码
- `quantity`: 数量
- `unit`: 单位
- `price`: 价格
- `path`: 图片地址（多张图片以分号`;`隔开）

## 使用示例

### 基础使用

```python
from api.stock_in_api import StockInAPI

# 初始化API
api = StockInAPI("https://st.pcylsoft.com:9006")

# 登录获取token
result = api.login("username", "password")
if result['code'] == 200:
    print("登录成功")
else:
    print(f"登录失败: {result['msg']}")
```

### 完整入库流程

```python
# 1. 获取货品列表
products = api.get_products_list()
print("可用货品:", products['data'])

# 2. 获取供应商列表
suppliers = api.get_suppliers_list()
print("供应商列表:", suppliers['data'])

# 3. 获取入库单号
index_result = api.get_stock_in_index()
stock_in_code = index_result['msg']
print(f"入库单号: {stock_in_code}")

# 4. 创建入库明细
details = []
detail1 = api.create_stock_in_detail(
    code="13010405",
    quantity="10.5",
    unit="市斤",
    price="5.50",
    path="images/product1.jpg;images/product2.jpg"
)
details.append(detail1)

# 5. 验证数据
validation = api.validate_stock_in_data(
    code=stock_in_code,
    datetime="2024-12-20",
    depot_code="S01",
    company_code="S001",
    details=details
)

if validation['valid']:
    # 6. 提交入库申请
    result = api.submit_stock_in(
        code=stock_in_code,
        datetime="2024-12-20",
        depot_code="S01",
        company_code="S001",
        details=details
    )
    
    if result['code'] == 200:
        print("入库成功!")
    else:
        print(f"入库失败: {result['msg']}")
else:
    print("数据验证失败:")
    for error in validation['errors']:
        print(f"  - {error}")
```

### 使用入库管理器

```python
from examples.stock_in_example import StockInManager

# 创建管理器
manager = StockInManager("username", "password")

# 登录
if manager.login():
    # 创建入库单
    items = [
        {
            'product_code': '13010405',
            'quantity': 10.5,
            'unit': '市斤',
            'price': 5.50,
            'images': ['images/product1.jpg']
        }
    ]
    
    result = manager.create_stock_in_order(
        depot_code='S01',
        company_code='S001',
        items=items
    )
    
    if result['success']:
        print(f"入库成功! 单号: {result['stock_in_code']}")
        print(result['summary'])
    else:
        print(f"入库失败: {result['message']}")
```

## 错误处理

API返回的错误信息格式：

```json
{
    "code": 错误码,
    "msg": "错误信息",
    "data": null
}
```

常见错误码：
- `401`: 未认证或token过期
- `500`: 服务器错误
- `200`: 成功

## 注意事项

1. 所有API调用都需要先登录获取token
2. Token需要放在Header的`Authorization`字段
3. 入库时间格式必须为`YYYY-MM-DD`
4. 图片路径多张时用分号`;`分隔
5. 数量和价格需要是有效的数字格式
6. 建议在提交前使用`validate_stock_in_data`验证数据

## 文件结构

```
api/
├── stock_in_api.py          # 入库API主文件
├── __init__.py              # 模块初始化文件（已更新）
examples/
├── stock_in_example.py      # 使用示例
test_stock_in_api.py         # 测试文件
docs/
├── stock_in_api_readme.md   # 本文档
```
