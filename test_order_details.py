#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单详情功能测试
Test Order Details Functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PyQt6.QtCore import QTimer
from ui.modules.order_module import OrderModule
from api.order_api import OrderAPI

class OrderDetailsTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("订单详情功能测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置背景
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
            }
        """)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建测试按钮区域
        button_layout = QHBoxLayout()
        
        # 测试API按钮
        test_api_btn = QPushButton("测试订单详情API")
        test_api_btn.setStyleSheet("""
            QPushButton {
                background: rgba(147, 51, 234, 0.3);
                color: white;
                border: 1px solid rgba(147, 51, 234, 0.5);
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: 600;
                margin: 5px;
            }
            QPushButton:hover {
                background: rgba(147, 51, 234, 0.5);
            }
        """)
        test_api_btn.clicked.connect(self.test_order_details_api)
        button_layout.addWidget(test_api_btn)
        
        # 模拟详情按钮
        mock_details_btn = QPushButton("模拟订单详情显示")
        mock_details_btn.setStyleSheet("""
            QPushButton {
                background: rgba(59, 130, 246, 0.3);
                color: white;
                border: 1px solid rgba(59, 130, 246, 0.5);
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: 600;
                margin: 5px;
            }
            QPushButton:hover {
                background: rgba(59, 130, 246, 0.5);
            }
        """)
        mock_details_btn.clicked.connect(self.test_mock_order_details)
        button_layout.addWidget(mock_details_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 创建订单模块
        self.order_module = OrderModule()
        layout.addWidget(self.order_module)
        
        print("🧪 订单详情功能测试")
        print("=" * 50)
        print("✅ 新增功能：")
        print("1. 订单详情API接口 (get_order_details)")
        print("2. 异步加载订单详情 (OrderDetailWorker)")
        print("3. 详细商品信息显示 (add_detailed_goods_section)")
        print("4. 商品卡片界面 (create_detailed_goods_card)")
        print("5. Glassmorphism风格详情界面")
        print("=" * 50)
        print("🔍 测试要点：")
        print("- 点击订单列表项是否能加载详细信息")
        print("- 详情界面是否显示完整的商品信息")
        print("- 商品卡片是否包含所有字段")
        print("- 界面样式是否符合Glassmorphism设计")
        print("- 异步加载是否正常工作")
        print("=" * 50)
        
        # 设置测试数据
        self.setup_test_data()
        
        # 10秒后自动关闭
        QTimer.singleShot(10000, self.close)
        
    def setup_test_data(self):
        """设置测试数据"""
        test_orders = [
            {
                'trade_no': 'CD22030712669',
                'circulate_name': '测试供应商(勿用)',
                'collect_time': '2022-03-07 23:10:20',
                'state': 0,
                'item_count': 2,
                'buy_quantity': '2',
                'deliver_quantity': '4.11',
                'goods': []  # 基本订单信息中的商品列表为空，详情需要通过API获取
            },
            {
                'trade_no': 'ORDER-2023-002',
                'circulate_name': '优质肉类供应商',
                'collect_time': '2023-12-01 14:15:00',
                'state': 1,
                'item_count': 3,
                'buy_quantity': '18',
                'deliver_quantity': '18',
                'goods': []
            }
        ]
        
        # 设置测试数据
        self.order_module.current_orders = test_orders
        self.order_module.populate_order_table(test_orders)
        print(f"📊 已加载 {len(test_orders)} 条测试订单数据")

    def test_order_details_api(self):
        """测试订单详情API"""
        print("\n🔧 测试订单详情API...")
        
        # 创建API实例
        api = OrderAPI("https://st.pcylsoft.com:9006/st/steelyard/")
        
        # 测试订单ID
        test_order_id = "CD22030712669"
        
        try:
            result = api.get_order_details(test_order_id, flag=1)
            print(f"API调用结果: {result}")
            
            if result.get('code') == 200:
                print("✅ API调用成功")
                data = result.get('data', {})
                goods = data.get('goods', [])
                print(f"📦 获取到 {len(goods)} 个商品详情")
                
                for i, good in enumerate(goods[:2]):  # 只显示前2个商品
                    print(f"  商品{i+1}: {good.get('name', '未知')} - {good.get('code', '无编码')}")
            else:
                print(f"❌ API调用失败: {result.get('msg', '未知错误')}")
                
        except Exception as e:
            print(f"❌ API测试异常: {e}")

    def test_mock_order_details(self):
        """测试模拟订单详情显示"""
        print("\n🎭 测试模拟订单详情显示...")
        
        # 模拟详细订单数据
        mock_detail_data = {
            'trade_no': 'CD22030712669',
            'item_count': 2,
            'receive_item_count': 0,
            'buy_quantity': '2',
            'deliver_quantity': '4.11',
            'collect_time': '2022-03-07 23:10:20',
            'state': 0,
            'circulate_name': '测试供应商(勿用)',
            'count': 2,
            'goods': [
                {
                    'id': 194933,
                    'spec': None,
                    'code': '13010501',
                    'name': '菠萝',
                    'buy_unit': '市斤',
                    'buy_quantity': '1',
                    'stock_quantity': 3,
                    'repair_receive': None,
                    'deliver_unit': '市斤',
                    'deliver_quantity': '3.11',
                    'receive_quantity': None,
                    'stock_mode': 1,
                    'receive_date': None,
                    'path': None,
                    'batch': None
                },
                {
                    'id': 194934,
                    'spec': '特级',
                    'code': '13010502',
                    'name': '草莓',
                    'buy_unit': '市斤',
                    'buy_quantity': '1',
                    'stock_quantity': 0,
                    'repair_receive': None,
                    'deliver_unit': '市斤',
                    'deliver_quantity': '1',
                    'receive_quantity': '0.8',
                    'stock_mode': 1,
                    'receive_date': '2022-03-08',
                    'path': None,
                    'batch': 'B20220308'
                }
            ]
        }
        
        # 直接显示详细信息
        self.order_module.display_detailed_order_info(mock_detail_data)
        print("✅ 模拟详情显示完成")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = OrderDetailsTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
