#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证功能演示脚本
Authentication Demo Script
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_auth_features():
    """演示认证功能"""
    print("=" * 60)
    print("🔐 智慧食堂管理系统 - 认证功能演示")
    print("=" * 60)
    
    try:
        from utils.auth_manager import auth_manager
        
        print("📋 新增认证功能特性:")
        print("  ✅ Token过期检查和自动登录")
        print("  ✅ 历史账号记忆和快速选择")
        print("  ✅ 认证信息持久化存储")
        print("  ✅ 启动时自动验证token")
        print("  ✅ 账号历史记录管理")
        
        print("\n" + "=" * 40)
        print("🎯 功能演示")
        print("=" * 40)
        
        # 演示1: 保存登录信息
        print("\n1️⃣ 保存登录信息演示")
        print("-" * 30)
        
        demo_users = [
            ("admin", "管理员账号"),
            ("teacher1", "教师账号"),
            ("canteen_manager", "食堂管理员")
        ]
        
        for username, description in demo_users:
            token = f"token_{username}_{int(time.time())}"
            auth_manager.save_login_success(username, token, expire_hours=24)
            print(f"✅ 保存 {username} ({description})")
        
        # 演示2: 查看历史记录
        print("\n2️⃣ 历史记录查看演示")
        print("-" * 30)
        
        history = auth_manager.get_login_history()
        print(f"📚 共有 {len(history)} 条历史记录:")
        
        for i, item in enumerate(history, 1):
            username = item.get('username', '')
            login_count = item.get('login_count', 0)
            last_login = item.get('last_login', 0)
            
            # 格式化时间
            last_login_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(last_login))
            print(f"  {i}. {username} | 登录{login_count}次 | 最后登录: {last_login_str}")
        
        # 演示3: Token状态检查
        print("\n3️⃣ Token状态检查演示")
        print("-" * 30)
        
        username, token = auth_manager.get_current_auth()
        if username and token:
            print(f"👤 当前用户: {username}")
            print(f"🔑 Token: {token[:20]}...")
            print(f"⏰ 状态: {'有效' if auth_manager.is_token_valid() else '已过期'}")
            
            expire_info = auth_manager.get_token_expire_info()
            if expire_info:
                print(f"📅 过期信息: {expire_info}")
        else:
            print("❌ 当前无有效认证信息")
        
        # 演示4: 存储文件信息
        print("\n4️⃣ 存储文件信息演示")
        print("-" * 30)
        
        storage_file = auth_manager.storage_file
        print(f"📁 存储文件: {storage_file}")
        
        if os.path.exists(storage_file):
            file_size = os.path.getsize(storage_file)
            print(f"📊 文件大小: {file_size} 字节")
            
            import json
            with open(storage_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            current_auth = data.get('current_auth', {})
            login_history = data.get('login_history', [])
            
            print(f"🔐 当前认证: {'有' if current_auth else '无'}")
            print(f"📚 历史记录: {len(login_history)} 条")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_ui_features():
    """演示UI功能"""
    print("\n" + "=" * 60)
    print("🎨 UI功能演示")
    print("=" * 60)
    
    print("📋 新增UI功能特性:")
    print("  ✅ 历史账号选择按钮")
    print("  ✅ 记住账号复选框")
    print("  ✅ Token状态显示标签")
    print("  ✅ 账号选择对话框")
    print("  ✅ 自动填充用户名")
    print("  ✅ Token过期提示")
    
    print("\n🖼️ 界面改进:")
    print("  • 登录界面增加历史账号快速选择")
    print("  • 显示Token过期时间和状态")
    print("  • 支持删除不需要的历史账号")
    print("  • 启动时自动检查Token有效性")
    print("  • 无需重复输入已记住的账号")

def demo_workflow():
    """演示工作流程"""
    print("\n" + "=" * 60)
    print("🔄 工作流程演示")
    print("=" * 60)
    
    print("📱 用户使用流程:")
    print()
    print("1️⃣ 首次登录:")
    print("   • 输入用户名和密码")
    print("   • 勾选'记住账号'选项")
    print("   • 点击登录按钮")
    print("   • 系统保存认证信息")
    print()
    print("2️⃣ 再次启动:")
    print("   • 系统自动检查Token")
    print("   • Token有效 → 直接进入主界面")
    print("   • Token过期 → 显示登录界面")
    print("   • 自动填充上次的用户名")
    print()
    print("3️⃣ 历史账号选择:")
    print("   • 点击'历史账号'按钮")
    print("   • 选择之前登录过的账号")
    print("   • 自动填充用户名")
    print("   • 只需输入密码即可登录")
    print()
    print("4️⃣ 账号管理:")
    print("   • 在历史账号对话框中")
    print("   • 可以删除不需要的账号")
    print("   • 查看登录次数和时间")
    print("   • 双击快速选择账号")

def main():
    """主演示函数"""
    print("🍽️ 智慧食堂管理系统 - 认证功能完整演示")
    
    # 演示认证功能
    if demo_auth_features():
        print("\n✅ 认证功能演示完成")
    else:
        print("\n❌ 认证功能演示失败")
        return
    
    # 演示UI功能
    demo_ui_features()
    
    # 演示工作流程
    demo_workflow()
    
    print("\n" + "=" * 60)
    print("🎉 演示完成")
    print("=" * 60)
    
    print("\n💡 如何体验新功能:")
    print("1. 运行程序: python main.py")
    print("2. 首次登录时勾选'记住账号'")
    print("3. 关闭程序后重新启动")
    print("4. 观察自动登录或Token状态显示")
    print("5. 点击'历史账号'按钮体验账号选择")
    
    print("\n🧪 测试新功能:")
    print("运行: python test_auth.py")
    
    print("\n📚 更多信息:")
    print("查看: README.md 和 USAGE.md")

if __name__ == "__main__":
    main()
