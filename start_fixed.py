#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版启动脚本
Fixed Startup Script
"""

import sys
import os
import traceback

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_imports():
    """检查所有必要的导入"""
    print("🔍 检查导入...")
    
    try:
        # 检查PyQt6
        from PyQt6.QtWidgets import QApplication, QDialog, QMainWindow
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QFont
        print("✅ PyQt6 导入成功")
        
        # 检查项目模块
        from config.settings import settings
        print("✅ 配置模块导入成功")
        
        from utils.auth_manager import auth_manager
        print("✅ 认证管理器导入成功")
        
        from api.auth_api import AuthAPI
        print("✅ API模块导入成功")
        
        from ui.styles import GlassmorphismStyles
        print("✅ 样式模块导入成功")
        
        from ui.login_window import LoginWindow
        print("✅ 登录窗口导入成功")
        
        from ui.main_window import MainWindow
        print("✅ 主窗口导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🍽️ 智慧食堂管理系统 - 修复版启动")
    print("=" * 60)
    
    # 检查导入
    if not check_imports():
        print("\n❌ 导入检查失败，程序无法启动")
        input("按回车键退出...")
        return
    
    print("\n🚀 启动应用程序...")
    
    try:
        # 创建应用程序实例
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QFont
        
        app = QApplication(sys.argv)
        
        # 设置应用程序属性
        app.setApplicationName("智慧食堂管理系统")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("智慧食堂")
        
        # 设置全局字体
        try:
            font = QFont("Microsoft YaHei", 10)
            app.setFont(font)
        except:
            pass
        
        print("✅ 应用程序创建成功")
        
        # 检查是否有有效的认证信息
        from utils.auth_manager import auth_manager
        username, token = auth_manager.get_current_auth()
        
        if username and token and auth_manager.is_token_valid():
            # 有有效token，直接进入主界面
            print(f"🔑 检测到有效token，用户: {username}")
            from ui.main_window import MainWindow
            main_window = MainWindow(token)
            main_window.show()
        else:
            # 没有有效token，显示登录窗口
            if username and not auth_manager.is_token_valid():
                print(f"⏰ Token已过期，用户: {username}")
            else:
                print("🆕 首次启动或无认证信息")
            
            from ui.login_window import LoginWindow
            login_window = LoginWindow()
            login_window.show()
        
        print("✅ 界面显示成功")
        print("📱 程序已启动，请查看窗口")
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装PyQt6: pip install PyQt6")
        input("按回车键退出...")
        
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
