#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摄像头依赖安装脚本
Camera Dependencies Installation Script
"""

import subprocess
import sys
import os

def install_package(package_name):
    """安装Python包"""
    try:
        print(f"正在安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装")
        return False

def main():
    """主函数"""
    print("🎯 摄像头功能依赖检查和安装")
    print("=" * 50)
    
    # 需要的包列表
    required_packages = [
        ("cv2", "opencv-python"),  # (import_name, package_name)
        ("PyQt6", "PyQt6"),
        ("serial", "pyserial"),
        ("requests", "requests")
    ]
    
    missing_packages = []
    
    # 检查已安装的包
    print("\n📋 检查已安装的包:")
    for import_name, package_name in required_packages:
        if not check_package(import_name):
            missing_packages.append(package_name)
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n🔧 需要安装 {len(missing_packages)} 个包:")
        for package in missing_packages:
            print(f"  - {package}")
        
        print("\n开始安装...")
        success_count = 0
        for package in missing_packages:
            if install_package(package):
                success_count += 1
        
        print(f"\n📊 安装结果: {success_count}/{len(missing_packages)} 成功")
        
        if success_count == len(missing_packages):
            print("🎉 所有依赖包安装成功！")
        else:
            print("⚠️  部分包安装失败，请手动安装")
    else:
        print("\n🎉 所有依赖包已安装！")
    
    # 测试摄像头
    print("\n📷 测试摄像头功能:")
    try:
        import cv2
        
        # 尝试打开摄像头
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            print("✅ 摄像头可用")
            
            # 读取一帧测试
            ret, frame = cap.read()
            if ret:
                print("✅ 摄像头读取正常")
                print(f"   分辨率: {frame.shape[1]}x{frame.shape[0]}")
            else:
                print("❌ 摄像头读取失败")
            
            cap.release()
        else:
            print("❌ 无法打开摄像头")
            print("   请检查:")
            print("   1. USB摄像头是否正确连接")
            print("   2. 摄像头驱动是否安装")
            print("   3. 摄像头是否被其他程序占用")
    
    except ImportError:
        print("❌ opencv-python 未正确安装")
    except Exception as e:
        print(f"❌ 摄像头测试失败: {e}")
    
    # 创建必要的目录
    print("\n📁 创建必要目录:")
    directories = ["photos", "logs"]
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ 目录 {directory}/ 已创建")
        except Exception as e:
            print(f"❌ 创建目录 {directory}/ 失败: {e}")
    
    print("\n🚀 安装完成！现在可以运行摄像头测试:")
    print("   python test_camera_integration.py")
    
    print("\n📖 使用说明:")
    print("1. 确保USB摄像头已连接")
    print("2. 运行测试程序")
    print("3. 点击'📹 启动'按钮启动摄像头")
    print("4. 点击'📸 拍照'按钮测试拍照")
    print("5. 点击'📤 提交'按钮测试完整流程")

if __name__ == "__main__":
    main()
