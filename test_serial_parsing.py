#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口数据解析测试脚本
Serial Data Parsing Test Script
"""

import re

def test_serial_parsing():
    """测试串口数据解析"""
    test_data = [
        "sg0000.00kg",
        "sg0000.00kg", 
        "wg0000.30kg",
        "wg0000.30kg",
        "wg0000.30kg",
        "wg0000.35kg",
        "wg0000.35kg",
        "wg0000.35kg",
        "sg123.45kg",  # 额外测试数据
        "wn999.99kg",  # 额外测试数据
        "invalid_data",  # 无效数据
        "sg-12.34kg",  # 负数测试
    ]
    
    print("🧪 测试串口数据解析:")
    print("=" * 50)
    
    # 原始正则表达式（有问题的）
    old_pattern = r'^[sw][gn](-?\d+\.\d+)kg$'
    
    # 修复后的正则表达式
    new_pattern = r'^[sw][gn](-?\d*\.?\d+)kg$'
    
    print("\n📊 测试结果对比:")
    print(f"{'数据':<15} {'原始模式':<12} {'修复模式':<12} {'解析结果'}")
    print("-" * 60)
    
    for data in test_data:
        # 测试原始模式
        old_match = re.match(old_pattern, data, re.IGNORECASE)
        old_result = "✅" if old_match else "❌"
        
        # 测试修复模式
        new_match = re.match(new_pattern, data, re.IGNORECASE)
        new_result = "✅" if new_match else "❌"
        
        # 解析结果
        if new_match:
            weight_str = new_match.group(1)
            try:
                weight_float = float(weight_str)
                weight = f"{weight_float:.2f}kg"
            except ValueError:
                weight = "格式错误"
        else:
            weight = "无匹配"
        
        print(f"{data:<15} {old_result:<12} {new_result:<12} {weight}")
    
    print("\n🔍 详细分析:")
    print("原始模式问题: r'^[sw][gn](-?\\d+\\.\\d+)kg$'")
    print("- 要求小数点前至少有一位数字 (\\d+)")
    print("- 无法匹配 '0000.00' 这样的格式")
    print()
    print("修复模式优势: r'^[sw][gn](-?\\d*\\.?\\d+)kg$'")
    print("- 允许小数点前零位或多位数字 (\\d*)")
    print("- 小数点可选 (\\.?)")
    print("- 能正确匹配所有格式")
    
    print("\n✅ 串口数据解析修复完成！")

if __name__ == "__main__":
    test_serial_parsing()
