#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输入框容器大小测试
Container Size Test
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_container_size():
    """测试输入框容器大小"""
    print("=" * 60)
    print("📦 输入框容器大小测试")
    print("=" * 60)
    
    try:
        from PyQt6.QtWidgets import QApplication, QLineEdit, QWidget, QVBoxLayout, QLabel, QHBoxLayout
        from ui.styles import GlassmorphismStyles
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建测试窗口
        window = QWidget()
        window.setWindowTitle("输入框容器大小测试")
        window.setFixedSize(500, 450)
        
        main_layout = QVBoxLayout(window)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title_label = QLabel("输入框容器大小测试")
        title_label.setStyleSheet("""
        QLabel {
            color: white;
            font-size: 18px;
            font-weight: 700;
            background: transparent;
            border: none;
            padding: 10px;
            text-align: center;
        }
        """)
        main_layout.addWidget(title_label)
        
        # 创建输入容器 - 模拟实际布局
        input_container = QWidget()
        input_layout = QVBoxLayout(input_container)
        input_layout.setSpacing(25)  # 与修复后一致
        input_layout.setContentsMargins(0, 10, 0, 10)  # 与修复后一致
        
        # 用户名区域
        username_container = QWidget()
        username_layout = QVBoxLayout(username_container)
        username_layout.setSpacing(12)  # 与修复后一致
        
        # 用户名标签和按钮
        username_header = QHBoxLayout()
        username_label = QLabel("用户名")
        username_label.setStyleSheet("color: white; font-size: 14px; font-weight: 500;")
        
        history_btn = QLabel("历史账号")  # 用标签模拟按钮
        history_btn.setStyleSheet("""
        QLabel {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            padding: 4px 8px;
        }
        """)
        history_btn.setFixedSize(80, 30)
        
        username_header.addWidget(username_label)
        username_header.addStretch()
        username_header.addWidget(history_btn)
        
        # 用户名输入框
        username_input = QLineEdit()
        username_input.setPlaceholderText("请输入用户名")
        username_input.setFixedHeight(60)  # 与修复后一致
        username_input.setStyleSheet(GlassmorphismStyles.get_input_style())
        username_input.setText("测试用户名显示效果")
        
        username_layout.addLayout(username_header)
        username_layout.addWidget(username_input)
        
        # 密码区域
        password_container = QWidget()
        password_layout = QVBoxLayout(password_container)
        password_layout.setSpacing(12)  # 与修复后一致
        
        password_label = QLabel("密码")
        password_label.setStyleSheet("color: white; font-size: 14px; font-weight: 500;")
        
        password_input = QLineEdit()
        password_input.setPlaceholderText("请输入密码")
        password_input.setFixedHeight(60)  # 与修复后一致
        password_input.setStyleSheet(GlassmorphismStyles.get_input_style())
        password_input.setText("测试密码显示效果")
        
        password_layout.addWidget(password_label)
        password_layout.addWidget(password_input)
        
        # 添加到输入容器
        input_layout.addWidget(username_container)
        input_layout.addWidget(password_container)
        
        # 添加到主布局
        main_layout.addWidget(input_container)
        
        # 添加说明
        info_label = QLabel("""
🔧 容器大小修复:
• 输入框高度: 60px (增加12px)
• 容器间距: 25px (增加5px)
• 内部间距: 12px (增加4px)
• 容器边距: 10px 上下 (新增)

✅ 预期效果:
• 输入框完整显示
• 文字清晰可见
• 容器空间充足
• 布局更加舒适
        """)
        info_label.setStyleSheet("""
        QLabel {
            color: rgba(255, 255, 255, 0.9);
            font-size: 11px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 12px;
            line-height: 1.4;
        }
        """)
        main_layout.addWidget(info_label)
        
        # 设置窗口背景
        window.setStyleSheet("""
        QWidget {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(30, 30, 60, 1),
                stop:0.5 rgba(60, 30, 90, 1),
                stop:1 rgba(90, 60, 120, 1));
        }
        """)
        
        print("✅ 容器大小测试窗口创建成功")
        print("📋 修复参数:")
        print("  • 输入框高度: 48px → 60px")
        print("  • 容器间距: 20px → 25px")
        print("  • 内部间距: 8px → 12px")
        print("  • 容器边距: 0 → 10px (上下)")
        
        print("\n💡 测试要点:")
        print("  1. 输入框应该完整显示，不被截断")
        print("  2. 文字应该清晰可见")
        print("  3. 容器应该有足够的空间")
        print("  4. 布局应该更加舒适")
        
        window.show()
        
        print("\n🖼️ 测试窗口已显示")
        print("请检查输入框和容器是否正常显示")
        print("按Ctrl+C退出测试")
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_fix_summary():
    """显示修复总结"""
    print("\n" + "=" * 60)
    print("📦 容器大小修复总结")
    print("=" * 60)
    
    print("🎯 问题:")
    print("  • 输入框还是不完整")
    print("  • 可能是高度的问题")
    print("  • 需要把容器弄大一点")
    
    print("\n✅ 修复方案:")
    print("  • 输入框高度: 48px → 60px")
    print("  • 容器间距: 20px → 25px")
    print("  • 内部间距: 8px → 12px")
    print("  • 添加容器边距: 10px (上下)")
    
    print("\n📁 修改的文件:")
    print("  • ui/login_window.py - 容器布局和间距")
    print("  • ui/styles.py - 输入框高度样式")
    
    print("\n🎨 预期效果:")
    print("  • 输入框有更多显示空间")
    print("  • 容器布局更加舒适")
    print("  • 文字完整清晰显示")
    print("  • 整体视觉效果更好")

def main():
    """主函数"""
    print("🍽️ 智慧食堂管理系统 - 容器大小修复测试")
    
    # 显示修复总结
    show_fix_summary()
    
    print("\n" + "=" * 60)
    print("🚀 启动容器大小测试")
    print("=" * 60)
    
    # 运行测试
    result = test_container_size()
    
    if result:
        print("\n✅ 容器大小测试完成")
        print("现在可以运行主程序验证效果:")
        print("  python main.py")
    else:
        print("\n❌ 容器大小测试失败")

if __name__ == "__main__":
    main()
