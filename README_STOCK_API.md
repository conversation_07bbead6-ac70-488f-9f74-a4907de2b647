# 库存管理API接口文档

## 概述

本文档介绍了新实现的库存管理API接口，包括仓库列表、项目列表、餐别列表、库存列表和出库提交等功能。

## 文件结构

```
api/
├── stock_api.py          # 库存管理API实现
├── auth_api.py           # 认证API（已存在）
├── weight_api.py         # 重量提交API（已存在）
└── __init__.py           # 模块初始化文件

test_stock_api.py         # 库存API测试文件
demo_stock_integration.py # 集成演示文件
```

## API接口列表

### 1. 仓库列表接口

**接口地址：** `https://st.pcylsoft.com:9006/st/steelyard/?op=stock_depot`  
**请求方式：** POST  
**Content-Type：** application/x-www-form-urlencoded  
**认证方式：** Header中的Authorization字段携带token

**使用示例：**
```python
from api.stock_api import StockAPI

api = StockAPI("https://st.pcylsoft.com:9006/st/steelyard/")
api.set_access_token("your_token")
result = api.get_depot_list()
```

**返回格式：**
```json
{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "code": "S01",
            "name": "食堂仓库",
            "number": "01"
        },
        {
            "code": "S02",
            "name": "调料仓库",
            "number": "02"
        }
    ],
    "total": 2
}
```

### 2. 项目列表接口

**接口地址：** `https://st.pcylsoft.com:9006/st/steelyard/?op=stock_project`  
**请求方式：** POST  
**Content-Type：** application/x-www-form-urlencoded

**使用示例：**
```python
result = api.get_project_list()
```

**返回格式：**
```json
{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "code": "P0001",
            "name": "非营养餐"
        },
        {
            "code": "P0002",
            "name": "营养餐"
        }
    ],
    "total": 2
}
```

### 3. 餐别列表接口

**接口地址：** `https://st.pcylsoft.com:9006/st/steelyard/?op=stock_time`  
**请求方式：** POST  
**Content-Type：** application/x-www-form-urlencoded

**使用示例：**
```python
result = api.get_meal_time_list()
```

**返回格式：**
```json
{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "value": 1,
            "name": "早餐"
        },
        {
            "value": 2,
            "name": "早点"
        },
        {
            "value": 4,
            "name": "午餐"
        },
        {
            "value": 8,
            "name": "午点"
        },
        {
            "value": 16,
            "name": "晚餐"
        },
        {
            "value": 32,
            "name": "晚点"
        }
    ],
    "total": 6
}
```

### 4. 库存列表接口

**接口地址：** `https://st.pcylsoft.com:9006/st/steelyard/?op=stocks`  
**请求方式：** POST  
**Content-Type：** application/x-www-form-urlencoded

**使用示例：**
```python
result = api.get_stock_list()
```

**返回格式：**
```json
{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "code": "13010405",
            "name": "小白菜",
            "unit": "市斤",
            "brand": null,
            "spec": null,
            "quantity": 33.3
        },
        {
            "code": "13010406",
            "name": "西红柿",
            "unit": "市斤",
            "brand": null,
            "spec": null,
            "quantity": 3.8
        }
    ],
    "total": 30
}
```

### 5. 出库提交接口

**接口地址：** `https://st.pcylsoft.com:9006/st/steelyard/?op=stock_submit`  
**请求方式：** POST  
**Content-Type：** application/json

**参数说明：**
- `datetime`: 出库日期，格式YYYY-MM-DD
- `depotcode`: 仓库代码（从仓库列表获取）
- `projectcode`: 项目代码（从项目列表获取）
- `timetype`: 餐别值（从餐别列表获取）
- `people`: 人数
- `details`: 出库明细列表
  - `code`: 商品代码
  - `quantity`: 出库数量（通过串口获取的重量）
  - `unit`: 单位（固定为"市斤"）
  - `path`: 图片地址（多张图片以分号;隔开）

**使用示例：**
```python
# 创建出库明细
details = [
    api.create_stock_detail(
        code="13010405",
        quantity="7.2",  # 从串口获取的重量
        unit="市斤",
        path="23/638702976856623258.jpg;"  # 照片路径
    )
]

# 提交出库申请
result = api.submit_stock_out(
    datetime="2024-12-20",
    depot_code="S01",
    project_code="P0001",
    time_type=1,
    people=100,
    details=details
)
```

**返回格式：**
```json
{
    "code": 200,
    "msg": "YO25073117941",
    "total": 0
}
```

## 集成说明

### 与现有功能的集成

1. **串口重量获取**
   - 参考：`ui/modules/weight_submission_module.py`
   - 通过串口获取重量数据，格式化为字符串

2. **照片拍摄**
   - 参考：`ui/modules/camera_module.py`
   - 通过摄像头拍摄照片并保存

3. **图片上传**
   - 参考：`api/weight_api.py` 的 `submit_picture` 方法
   - 将照片上传到服务器获取路径

### 完整工作流程

```python
from api.stock_api import StockAPI
from api.auth_api import AuthAPI

# 1. 登录获取token
auth_api = AuthAPI("https://st.pcylsoft.com:9006/st/steelyard/")
login_result = auth_api.login("username", "password")

# 2. 创建库存API实例
stock_api = StockAPI("https://st.pcylsoft.com:9006/st/steelyard/")
stock_api.set_access_token(auth_api.access_token)

# 3. 获取基础数据
depots = stock_api.get_depot_list()
projects = stock_api.get_project_list()
meal_times = stock_api.get_meal_time_list()
stocks = stock_api.get_stock_list()

# 4. 获取重量（从串口）
weight = get_weight_from_serial()  # 实现串口读取

# 5. 拍摄照片
photo_path = capture_photo()  # 实现摄像头拍照

# 6. 上传照片获取路径
photo_result = upload_photo(photo_path)  # 实现图片上传

# 7. 创建出库明细
details = [
    stock_api.create_stock_detail(
        code="13010405",
        quantity=weight,
        unit="市斤",
        path=photo_result['path']
    )
]

# 8. 验证数据
validation = stock_api.validate_stock_out_data(
    datetime="2024-12-20",
    depot_code="S01",
    project_code="P0001",
    time_type=1,
    people=100,
    details=details
)

# 9. 提交出库申请
if validation['valid']:
    result = stock_api.submit_stock_out(
        datetime="2024-12-20",
        depot_code="S01",
        project_code="P0001",
        time_type=1,
        people=100,
        details=details
    )
    print(f"出库单号: {result['msg']}")
```

## 工具方法

### 数据验证
```python
validation = api.validate_stock_out_data(datetime, depot_code, project_code, time_type, people, details)
if not validation['valid']:
    for error in validation['errors']:
        print(f"错误: {error}")
```

### 餐别名称获取
```python
meal_name = api.get_meal_time_name(1)  # 返回"早餐"
```

### 出库明细创建
```python
detail = api.create_stock_detail(
    code="商品代码",
    quantity="重量",
    unit="市斤",
    path="图片路径"
)
```

## 注意事项

1. **认证要求**：所有接口都需要在Header中携带Authorization字段的token
2. **重量获取**：重量数据通过串口获取，参考现有的串口处理代码
3. **照片处理**：照片通过摄像头拍摄并上传，参考现有的照片处理代码
4. **单位固定**：出库明细中的单位固定为"市斤"
5. **数据验证**：提交前建议使用验证方法检查数据完整性

## 测试文件

- `test_stock_api.py`: 基本API测试
- `demo_stock_integration.py`: 集成演示
- `simple_stock_test.py`: 简单功能测试
