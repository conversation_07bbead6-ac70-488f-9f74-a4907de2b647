#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摄像头集成测试脚本
Camera Integration Test Script
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

# 导入重量提交模块
from ui.modules.weight_submission_module import WeightSubmissionModule
from api.weight_api import WeightAPI

class TestCameraWindow(QMainWindow):
    """摄像头测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("摄像头集成测试 - 重量提交 + 拍照")
        self.setGeometry(100, 100, 900, 700)
        
        # 设置深色背景
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a1a2e, stop:0.5 #16213e, stop:1 #0f3460);
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 添加标题
        title = QLabel("🎯 摄像头 + 重量提交集成测试")
        title.setFont(QFont("Inter", 16, QFont.Weight.Bold))
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                text-align: center;
                padding: 10px;
                background: rgba(147, 51, 234, 0.2);
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 添加重量提交模块
        self.weight_module = WeightSubmissionModule()
        
        # 设置测试数据和API
        self.setup_test_data()
        
        layout.addWidget(self.weight_module)
        
        # 添加测试按钮
        self.create_test_buttons(layout)
    
    def setup_test_data(self):
        """设置测试数据"""
        # 初始化API（使用正确的完整URL）
        try:
            api = WeightAPI("https://st.pcylsoft.com:9006/st/steelyard/")
            # 注意：实际使用时需要先登录获取token
            # api.set_token("your_test_token_here")
            self.weight_module.api = api
            print("✅ API初始化成功")
            print(f"📡 重量提交接口: https://st.pcylsoft.com:9006/st/steelyard/?op=weight")
            print(f"📡 图片提交接口: https://st.pcylsoft.com:9006/st/steelyard/?op=picture")
        except Exception as e:
            print(f"❌ API初始化失败: {e}")
        
        # 模拟商品信息（根据API响应格式）
        test_product = {
            'id': 194933,  # 商品ID（用于重量提交API）
            'spec': None,
            'code': '13010501',  # 商品编码
            'name': '菠萝',  # 商品名称
            'buy_unit': '市斤',
            'buy_quantity': '1',
            'stock_quantity': 3,
            'repair_receive': None,
            'deliver_unit': '市斤',
            'deliver_quantity': '3.11',
            'receive_quantity': None,
            'stock_mode': 1,  # 入库模式：1=入库
            'receive_date': None,
            'path': None,
            'batch': None
        }

        # 直接设置商品数据对象
        self.weight_module.set_product_info(test_product)
        
        # 设置当前商品数据
        self.weight_module.current_product = test_product
        
        # 模拟重量数据
        self.weight_module.current_weight_label.setText("2.35 kg")
        
        # 模拟串口连接状态
        self.weight_module.serial_status_label.setText("🟢 串口已连接 (COM4)")
        
        # 添加一些测试重量
        test_weights = [2.30, 2.35, 2.32]
        for weight in test_weights:
            self.weight_module.current_weights.append(weight)
        
        self.weight_module.update_weight_list_display()
        
        # 更新状态
        self.weight_module.status_label.setText(f"✅ 已添加 {len(test_weights)} 个重量记录")
    
    def create_test_buttons(self, parent_layout):
        """创建测试按钮"""
        button_layout = QVBoxLayout()
        
        # 测试拍照按钮
        test_photo_btn = QPushButton("📸 测试拍照功能")
        test_photo_btn.setFont(QFont("Inter", 12, QFont.Weight.Medium))
        test_photo_btn.setStyleSheet("""
            QPushButton {
                background: rgba(59, 130, 246, 0.2);
                border: 1px solid rgba(59, 130, 246, 0.3);
                border-radius: 6px;
                padding: 8px 16px;
                color: white;
                font-size: 12px;
                min-height: 35px;
            }
            QPushButton:hover {
                background: rgba(59, 130, 246, 0.3);
            }
        """)
        test_photo_btn.clicked.connect(self.test_photo_function)
        button_layout.addWidget(test_photo_btn)
        
        # 测试提交按钮
        test_submit_btn = QPushButton("🚀 测试完整提交流程")
        test_submit_btn.setFont(QFont("Inter", 12, QFont.Weight.Medium))
        test_submit_btn.setStyleSheet("""
            QPushButton {
                background: rgba(147, 51, 234, 0.2);
                border: 1px solid rgba(147, 51, 234, 0.3);
                border-radius: 6px;
                padding: 8px 16px;
                color: white;
                font-size: 12px;
                min-height: 35px;
            }
            QPushButton:hover {
                background: rgba(147, 51, 234, 0.3);
            }
        """)
        test_submit_btn.clicked.connect(self.test_submit_function)
        button_layout.addWidget(test_submit_btn)
        
        parent_layout.addLayout(button_layout)
    
    def test_photo_function(self):
        """测试拍照功能"""
        if hasattr(self.weight_module, 'camera_module') and self.weight_module.camera_module:
            if not self.weight_module.camera_module.is_camera_active:
                self.weight_module.camera_module.start_camera()
            else:
                self.weight_module.camera_module.capture_photo()
        else:
            print("❌ 摄像头模块不可用")
    
    def test_submit_function(self):
        """测试完整提交流程"""
        print("🚀 开始测试完整提交流程...")
        print("1. 重量数据:", self.weight_module.current_weights)
        print("2. 商品信息:", self.weight_module.current_product)
        print("3. 摄像头状态:", "可用" if hasattr(self.weight_module, 'camera_module') and self.weight_module.camera_module else "不可用")
        
        # 调用提交方法
        self.weight_module.submit_weights()
    
    def closeEvent(self, event):
        """关闭事件"""
        if hasattr(self.weight_module, 'camera_module') and self.weight_module.camera_module:
            self.weight_module.camera_module.cleanup()
        super().closeEvent(event)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序字体
    font = QFont("Inter", 10)
    app.setFont(font)
    
    # 创建测试窗口
    window = TestCameraWindow()
    window.show()
    
    print("🎯 摄像头集成测试")
    print("=" * 60)
    print("✅ 新增功能:")
    print("1. USB摄像头实时预览")
    print("2. 手动拍照功能")
    print("3. 自动拍照（提交时）")
    print("4. 图片上传API集成")
    print("5. 重量+图片一体化提交")
    print()
    print("🔧 测试步骤:")
    print("1. 点击'📹 启动'按钮启动摄像头")
    print("2. 点击'📸 拍照'按钮测试拍照")
    print("3. 点击'📤 提交'按钮测试完整流程")
    print()
    print("📋 提交流程:")
    print("1. 提交重量数据到API")
    print("2. 自动拍照（如果摄像头开启）")
    print("3. 提交图片到API")
    print("4. 显示完整结果")
    print()
    print("⚠️  注意事项:")
    print("- 需要连接USB摄像头")
    print("- 需要安装opencv-python: pip install opencv-python")
    print("- 需要有效的API token进行实际提交")
    print("- 图片保存在photos/目录下")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
