#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单串口测试
"""

def test_serial_import():
    """测试串口模块导入"""
    try:
        import serial
        import serial.tools.list_ports
        print("✅ 串口模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 串口模块导入失败: {e}")
        return False

def list_ports():
    """列出串口"""
    try:
        import serial.tools.list_ports
        ports = serial.tools.list_ports.comports()
        
        print(f"发现 {len(ports)} 个串口:")
        for port in ports:
            print(f"  {port.device}: {port.description}")
        
        return [port.device for port in ports]
    except Exception as e:
        print(f"❌ 列出串口失败: {e}")
        return []

def test_port_access(port_name):
    """测试端口访问"""
    try:
        import serial
        ser = serial.Serial(port_name, 9600, timeout=0.1)
        print(f"✅ 端口 {port_name} 可访问")
        ser.close()
        return True
    except Exception as e:
        print(f"❌ 端口 {port_name} 访问失败: {e}")
        return False

def main():
    print("🚀 简单串口测试")
    print("=" * 30)
    
    # 测试导入
    if not test_serial_import():
        return
    
    # 列出端口
    ports = list_ports()
    
    # 测试端口访问
    for port in ports:
        test_port_access(port)

if __name__ == "__main__":
    main()
