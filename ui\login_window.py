#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录窗口
Login Window
"""

import sys
import json
import requests
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QFrame, QMessageBox,
    QApplication, QGraphicsDropShadowEffect, QSpacerItem, QSizePolicy,
    QComboBox, QCheckBox, QListWidget, QListWidgetItem, QDialog
)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor, QBrush, QLinearGradient

try:
    from .styles import GlassmorphismStyles
except ImportError:
    from ui.styles import GlassmorphismStyles

try:
    from api.auth_api import AuthAPI
    from config.settings import settings
    from utils.auth_manager import auth_manager
except ImportError as e:
    print(f"导入错误: {e}")
    # 创建默认的API和设置类
    class AuthAPI:
        def __init__(self, base_url=""):
            pass
        def login(self, username, password, version):
            return {"code": 500, "msg": "API模块未正确导入"}
        def verify_token(self):
            return {"code": 500, "msg": "API模块未正确导入"}

    class Settings:
        app_name = "智慧食堂管理系统"
        app_version = "1.0.0"
        api_base_url = "https://example.com"
        api_version = "1.8.13"
        fullscreen = False
        window_width = 1200
        window_height = 800

    class AuthManager:
        def get_current_auth(self):
            return None, None
        def is_token_valid(self):
            return False
        def get_login_history(self):
            return []
        def save_login_success(self, username, token, expire_hours=24):
            pass
        def clear_current_auth(self):
            pass

    settings = Settings()
    auth_manager = AuthManager()

class LoginThread(QThread):
    """登录线程"""
    login_success = pyqtSignal(dict)  # 登录成功信号
    login_failed = pyqtSignal(str)    # 登录失败信号

    def __init__(self, username, password, version=None):
        super().__init__()
        self.username = username
        self.password = password
        self.version = version or settings.api_version
        self.auth_api = AuthAPI(settings.api_base_url)

    def run(self):
        """执行登录"""
        try:
            result = self.auth_api.login(self.username, self.password, self.version)
            if result.get('code') == 200:
                self.login_success.emit(result.get('data', {}))
            else:
                self.login_failed.emit(result.get('msg', '登录失败'))
        except Exception as e:
            self.login_failed.emit(f"网络错误: {str(e)}")

class TokenVerifyThread(QThread):
    """Token验证线程"""
    verify_success = pyqtSignal(dict)  # 验证成功信号
    verify_failed = pyqtSignal(str)    # 验证失败信号

    def __init__(self, token):
        super().__init__()
        self.token = token
        self.auth_api = AuthAPI(settings.api_base_url)

    def run(self):
        """执行验证"""
        try:
            # 设置token
            self.auth_api.set_access_token(self.token)

            # 验证token
            result = self.auth_api.verify_token()
            if result.get('code') == 200:
                self.verify_success.emit(result.get('data', {}))
            else:
                self.verify_failed.emit(result.get('msg', 'Token验证失败'))
        except Exception as e:
            self.verify_failed.emit(f"验证错误: {str(e)}")

class AccountSelectionDialog(QDialog):
    """账号选择对话框"""

    def __init__(self, parent=None, history_list=None):
        super().__init__(parent)
        self.history_list = history_list or []
        self.selected_username = None

        self.setWindowTitle("选择历史账号")
        self.setFixedSize(350, 400)
        self.setModal(True)

        self.init_ui()
        self.apply_styles()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("选择历史登录账号")
        title_label.setObjectName("dialogTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 账号列表
        self.account_list = QListWidget()
        self.account_list.setObjectName("accountList")

        for item in self.history_list:
            username = item.get('username', '')
            last_login = item.get('last_login', 0)
            login_count = item.get('login_count', 0)

            # 格式化时间
            import time
            last_login_str = time.strftime('%Y-%m-%d %H:%M', time.localtime(last_login))

            list_item = QListWidgetItem(f"{username}\n最后登录: {last_login_str} | 登录次数: {login_count}")
            list_item.setData(Qt.ItemDataRole.UserRole, username)
            self.account_list.addItem(list_item)

        self.account_list.itemDoubleClicked.connect(self.on_item_double_clicked)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.select_btn = QPushButton("选择")
        self.select_btn.clicked.connect(self.select_account)
        self.select_btn.setEnabled(False)

        self.delete_btn = QPushButton("删除")
        self.delete_btn.clicked.connect(self.delete_account)
        self.delete_btn.setEnabled(False)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(self.delete_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.select_btn)
        button_layout.addWidget(self.cancel_btn)

        # 选择改变事件
        self.account_list.itemSelectionChanged.connect(self.on_selection_changed)

        layout.addWidget(title_label)
        layout.addWidget(self.account_list)
        layout.addLayout(button_layout)

    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
        QDialog {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(30, 30, 60, 1),
                stop:0.5 rgba(60, 30, 90, 1),
                stop:1 rgba(90, 60, 120, 1));
        }
        QLabel#dialogTitle {
            color: white;
            font-size: 16px;
            font-weight: 600;
            background: transparent;
            border: none;
        }
        QListWidget#accountList {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: white;
            padding: 8px;
        }
        QListWidget#accountList::item {
            padding: 12px;
            border-radius: 8px;
            margin: 2px;
        }
        QListWidget#accountList::item:selected {
            background: rgba(147, 51, 234, 0.3);
        }
        QListWidget#accountList::item:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        QPushButton {
            background: rgba(147, 51, 234, 0.2);
            border: 1px solid rgba(147, 51, 234, 0.3);
            border-radius: 8px;
            padding: 8px 16px;
            color: white;
            font-weight: 500;
        }
        QPushButton:hover {
            background: rgba(147, 51, 234, 0.3);
        }
        QPushButton:disabled {
            background: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.3);
        }
        """)

    def on_selection_changed(self):
        """选择改变事件"""
        has_selection = len(self.account_list.selectedItems()) > 0
        self.select_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def on_item_double_clicked(self, item):
        """双击事件"""
        self.selected_username = item.data(Qt.ItemDataRole.UserRole)
        self.accept()

    def select_account(self):
        """选择账号"""
        current_item = self.account_list.currentItem()
        if current_item:
            self.selected_username = current_item.data(Qt.ItemDataRole.UserRole)
            self.accept()

    def delete_account(self):
        """删除账号"""
        current_item = self.account_list.currentItem()
        if current_item:
            username = current_item.data(Qt.ItemDataRole.UserRole)

            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除账号 '{username}' 的历史记录吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # 从认证管理器中删除
                auth_manager.remove_from_history(username)

                # 从列表中移除
                row = self.account_list.row(current_item)
                self.account_list.takeItem(row)

                QMessageBox.information(self, "成功", "账号历史记录已删除")

    def get_selected_username(self):
        """获取选中的用户名"""
        return self.selected_username

class LoginWindow(QMainWindow):
    """登录窗口类"""

    def __init__(self, skip_token_check=False):
        super().__init__()
        self.skip_token_check = skip_token_check  # 是否跳过token检查
        self.setWindowTitle(f"{settings.app_name} - 登录")

        # 根据配置设置窗口大小
        if settings.fullscreen:
            self.showMaximized()
        else:
            self.setFixedSize(settings.window_width, settings.window_height)

        # 设置窗口属性
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # 登录线程
        self.login_thread = None

        # 初始化UI
        self.init_ui()
        self.apply_styles()

        # 居中显示
        self.center_window()

        # 检查自动登录（如果不跳过token检查）
        if not self.skip_token_check:
            QTimer.singleShot(500, self.check_auto_login)
        else:
            # 跳过token检查，直接加载历史账号
            QTimer.singleShot(100, self.load_username_history)
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建登录卡片
        self.create_login_card(main_layout)
    
    def create_login_card(self, parent_layout):
        """创建登录卡片"""
        # 创建居中容器
        center_container = QWidget()
        center_layout = QVBoxLayout(center_container)
        center_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 登录卡片
        self.login_card = QFrame()
        self.login_card.setFixedSize(450, 650)  # 增加高度以容纳完整的输入框
        self.login_card.setObjectName("loginCard")
        
        # 卡片布局
        card_layout = QVBoxLayout(self.login_card)
        card_layout.setSpacing(25)  # 减少间距以容纳更多内容
        card_layout.setContentsMargins(40, 40, 40, 40)  # 减少边距
        
        # 标题区域
        self.create_title_section(card_layout)
        
        # 输入区域
        self.create_input_section(card_layout)
        
        # 按钮区域
        self.create_button_section(card_layout)
        
        # 底部信息
        self.create_footer_section(card_layout)
        
        # 添加阴影效果
        self.add_shadow_effect(self.login_card)
        
        center_layout.addWidget(self.login_card)
        parent_layout.addWidget(center_container)
    
    def create_title_section(self, layout):
        """创建标题区域"""
        title_container = QWidget()
        title_layout = QVBoxLayout(title_container)
        title_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.setSpacing(10)
        
        # 主标题
        self.title_label = QLabel("智慧食堂管理系统")
        self.title_label.setObjectName("titleLabel")
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 副标题
        self.subtitle_label = QLabel("Smart Canteen Management System")
        self.subtitle_label.setObjectName("subtitleLabel")
        self.subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        title_layout.addWidget(self.title_label)
        title_layout.addWidget(self.subtitle_label)
        
        layout.addWidget(title_container)
    
    def create_input_section(self, layout):
        """创建输入区域"""
        input_container = QWidget()
        input_layout = QVBoxLayout(input_container)
        input_layout.setSpacing(20)  # 适中的输入区域间距
        input_layout.setContentsMargins(0, 5, 0, 5)  # 减少上下边距

        # 用户名输入区域
        username_container = QWidget()
        username_container.setMinimumHeight(100)  # 确保容器有足够高度
        username_layout = QVBoxLayout(username_container)
        username_layout.setSpacing(10)  # 适中的间距

        # 用户名标签和历史按钮
        username_header = QHBoxLayout()
        username_label = QLabel("用户名")
        username_label.setObjectName("inputLabel")

        self.history_btn = QPushButton("历史账号")
        self.history_btn.setObjectName("historyButton")
        self.history_btn.setFixedSize(80, 30)
        self.history_btn.clicked.connect(self.show_history_accounts)

        username_header.addWidget(username_label)
        username_header.addStretch()
        username_header.addWidget(self.history_btn)

        # 用户名输入框
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("请输入用户名")
        self.username_input.setObjectName("usernameInput")
        self.username_input.setFixedHeight(60)  # 设置固定高度
        from PyQt6.QtWidgets import QSizePolicy
        self.username_input.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.username_input.textChanged.connect(self.on_username_changed)

        username_layout.addLayout(username_header)
        username_layout.addWidget(self.username_input)

        # 密码输入
        password_container = QWidget()
        password_container.setMinimumHeight(100)  # 确保容器有足够高度
        password_layout = QVBoxLayout(password_container)
        password_layout.setSpacing(10)  # 适中的间距

        password_label = QLabel("密码")
        password_label.setObjectName("inputLabel")

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setObjectName("passwordInput")
        self.password_input.setFixedHeight(60)  # 设置固定高度
        self.password_input.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)

        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)

        # 记住密码选项
        options_container = QWidget()
        options_layout = QHBoxLayout(options_container)
        options_layout.setContentsMargins(0, 0, 0, 0)

        self.remember_checkbox = QCheckBox("记住账号")
        self.remember_checkbox.setObjectName("rememberCheckbox")
        self.remember_checkbox.setChecked(True)

        # Token状态显示
        self.token_status_label = QLabel("")
        self.token_status_label.setObjectName("tokenStatusLabel")
        self.token_status_label.setAlignment(Qt.AlignmentFlag.AlignRight)

        options_layout.addWidget(self.remember_checkbox)
        options_layout.addStretch()
        options_layout.addWidget(self.token_status_label)

        input_layout.addWidget(username_container)
        input_layout.addWidget(password_container)
        input_layout.addWidget(options_container)

        layout.addWidget(input_container)
    
    def create_button_section(self, layout):
        """创建按钮区域"""
        button_container = QWidget()
        button_layout = QVBoxLayout(button_container)
        button_layout.setSpacing(15)
        
        # 登录按钮
        self.login_button = QPushButton("登录")
        self.login_button.setObjectName("loginButton")
        self.login_button.setFixedHeight(50)
        self.login_button.clicked.connect(self.handle_login)
        
        # 绑定回车键
        self.username_input.returnPressed.connect(self.handle_login)
        self.password_input.returnPressed.connect(self.handle_login)
        
        button_layout.addWidget(self.login_button)
        
        layout.addWidget(button_container)
    
    def create_footer_section(self, layout):
        """创建底部区域"""
        footer_container = QWidget()
        footer_layout = QVBoxLayout(footer_container)
        footer_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 版本信息
        version_label = QLabel(f"版本 {settings.app_version}")
        version_label.setObjectName("versionLabel")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        footer_layout.addWidget(version_label)
        
        layout.addWidget(footer_container)

    def check_auto_login(self):
        """检查自动登录"""
        username, token = auth_manager.get_current_auth()

        if username and token:
            # 显示token状态
            expire_info = auth_manager.get_token_expire_info()
            if expire_info:
                self.token_status_label.setText(f"Token {expire_info}")

            # 填充用户名
            self.username_input.setText(username)

            # 验证token是否有效
            self.login_button.setEnabled(False)
            self.login_button.setText("验证中...")

            # 创建验证线程
            self.verify_thread = TokenVerifyThread(token)
            self.verify_thread.verify_success.connect(self.on_token_valid)
            self.verify_thread.verify_failed.connect(self.on_token_invalid)
            self.verify_thread.start()
        else:
            # 加载历史账号到用户名输入框
            self.load_username_history()

    def load_username_history(self):
        """加载用户名历史"""
        history = auth_manager.get_login_history()
        if history:
            # 设置最近使用的用户名
            self.username_input.setText(history[0].get('username', ''))

    def on_username_changed(self, text):
        """用户名改变事件"""
        # 清除token状态显示
        self.token_status_label.setText("")

    def show_history_accounts(self):
        """显示历史账号选择"""
        history = auth_manager.get_login_history()
        if not history:
            self.show_message("提示", "暂无历史登录账号", QMessageBox.Icon.Information)
            return

        # 创建账号选择对话框
        dialog = AccountSelectionDialog(self, history)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            selected_username = dialog.get_selected_username()
            if selected_username:
                self.username_input.setText(selected_username)
                self.password_input.setFocus()

    def on_token_valid(self, user_data):
        """Token验证成功"""
        self.login_button.setEnabled(True)
        self.login_button.setText("登录")

        # 直接跳转到主界面
        username, token = auth_manager.get_current_auth()
        if username and token:
            self.open_main_window(token)

    def on_token_invalid(self, error_msg):
        """Token验证失败"""
        self.login_button.setEnabled(True)
        self.login_button.setText("登录")

        # 清除无效的认证信息
        auth_manager.clear_current_auth()
        self.token_status_label.setText("需要重新登录")

        # 焦点到密码输入框
        self.password_input.setFocus()

    def add_shadow_effect(self, widget):
        """添加阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 10)
        widget.setGraphicsEffect(shadow)

    def apply_styles(self):
        """应用样式"""
        # 主窗口背景
        self.setStyleSheet(GlassmorphismStyles.get_main_window_style())

        # 登录卡片样式
        self.login_card.setStyleSheet("""
        QFrame#loginCard {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
        }
        """)

        # 标题样式
        self.title_label.setStyleSheet("""
        QLabel#titleLabel {
            color: white;
            font-size: 28px;
            font-weight: 700;
            background: transparent;
            border: none;
        }
        """)

        self.subtitle_label.setStyleSheet("""
        QLabel#subtitleLabel {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            font-weight: 400;
            background: transparent;
            border: none;
        }
        """)

        # 输入框标签样式
        input_label_style = """
        QLabel#inputLabel {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-weight: 500;
            background: transparent;
            border: none;
        }
        """

        # 应用到所有输入标签
        for label in self.findChildren(QLabel):
            if label.objectName() == "inputLabel":
                label.setStyleSheet(input_label_style)

        # 输入框样式
        self.username_input.setStyleSheet(GlassmorphismStyles.get_input_style())
        self.password_input.setStyleSheet(GlassmorphismStyles.get_input_style())

        # 按钮样式
        self.login_button.setStyleSheet(GlassmorphismStyles.get_button_style())

        # 版本标签样式
        version_label = self.findChild(QLabel, "versionLabel")
        if version_label:
            version_label.setStyleSheet("""
            QLabel#versionLabel {
                color: rgba(255, 255, 255, 0.6);
                font-size: 12px;
                font-weight: 400;
                background: transparent;
                border: none;
            }
            """)

        # 历史按钮样式
        self.history_btn.setStyleSheet("""
        QPushButton#historyButton {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            padding: 4px 8px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-weight: 400;
        }
        QPushButton#historyButton:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        """)

        # 复选框样式
        self.remember_checkbox.setStyleSheet("""
        QCheckBox#rememberCheckbox {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            spacing: 8px;
        }
        QCheckBox#rememberCheckbox::indicator {
            width: 16px;
            height: 16px;
            border-radius: 3px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.1);
        }
        QCheckBox#rememberCheckbox::indicator:checked {
            background: rgba(147, 51, 234, 0.3);
            border: 1px solid rgba(147, 51, 234, 0.5);
        }
        """)

        # Token状态标签样式
        self.token_status_label.setStyleSheet("""
        QLabel#tokenStatusLabel {
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            font-weight: 400;
            background: transparent;
            border: none;
        }
        """)

    def center_window(self):
        """居中显示窗口"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def handle_login(self):
        """处理登录"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        # 验证输入
        if not username:
            self.show_message("错误", "请输入用户名", QMessageBox.Icon.Warning)
            self.username_input.setFocus()
            return

        if not password:
            self.show_message("错误", "请输入密码", QMessageBox.Icon.Warning)
            self.password_input.setFocus()
            return

        # 禁用登录按钮
        self.login_button.setEnabled(False)
        self.login_button.setText("登录中...")

        # 创建并启动登录线程
        self.login_thread = LoginThread(username, password)
        self.login_thread.login_success.connect(self.on_login_success)
        self.login_thread.login_failed.connect(self.on_login_failed)
        self.login_thread.start()

    def on_login_success(self, data):
        """登录成功处理"""
        self.login_button.setEnabled(True)
        self.login_button.setText("登录")

        # 保存登录信息
        access_token = data.get('access_token')
        picture_upload_url = data.get('picture_upload_url')

        if access_token:
            username = self.username_input.text().strip()

            # 保存认证信息到认证管理器
            if self.remember_checkbox.isChecked():
                auth_manager.save_login_success(username, access_token, expire_hours=24)
                print(f"登录成功，已保存认证信息")

            print(f"登录成功，Token: {access_token}")
            print(f"图片上传地址: {picture_upload_url}")

            # 跳转到主界面
            self.open_main_window(access_token)

        else:
            self.show_message("错误", "登录响应数据异常", QMessageBox.Icon.Critical)

    def on_login_failed(self, error_msg):
        """登录失败处理"""
        self.login_button.setEnabled(True)
        self.login_button.setText("登录")

        self.show_message("登录失败", error_msg, QMessageBox.Icon.Critical)

    def show_message(self, title, message, icon=QMessageBox.Icon.Information):
        """显示消息框"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(icon)
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)

        # 应用玻璃态样式到消息框
        msg_box.setStyleSheet("""
        QMessageBox {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: white;
        }
        QMessageBox QPushButton {
            background: rgba(147, 51, 234, 0.2);
            border: 1px solid rgba(147, 51, 234, 0.3);
            border-radius: 8px;
            padding: 8px 16px;
            color: white;
            font-weight: 500;
        }
        QMessageBox QPushButton:hover {
            background: rgba(147, 51, 234, 0.3);
        }
        """)

        msg_box.exec()

    def open_main_window(self, access_token: str):
        """打开主界面"""
        from .main_window import MainWindow

        # 标记正在跳转到主界面，避免closeEvent退出应用程序
        self.is_opening_main_window = True

        # 创建主界面
        self.main_window = MainWindow(access_token)
        self.main_window.show()

        # 关闭登录窗口
        self.close()

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 如果是跳转到主界面，不退出应用程序
        if hasattr(self, 'is_opening_main_window') and self.is_opening_main_window:
            event.accept()
            return

        # 如果没有主窗口在运行，退出应用程序
        if not hasattr(self, 'main_window') or not self.main_window or not self.main_window.isVisible():
            from PyQt6.QtWidgets import QApplication
            QApplication.quit()
        event.accept()

    def paintEvent(self, event):
        """绘制背景"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 创建渐变背景
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(30, 30, 60))
        gradient.setColorAt(0.5, QColor(60, 30, 90))
        gradient.setColorAt(1, QColor(90, 60, 120))

        painter.fillRect(self.rect(), QBrush(gradient))

    def mousePressEvent(self, event):
        """鼠标按下事件 - 用于拖拽窗口"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 用于拖拽窗口"""
        if event.buttons() == Qt.MouseButton.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

    def keyPressEvent(self, event):
        """键盘事件"""
        if event.key() == Qt.Key.Key_Escape:
            self.close()
        super().keyPressEvent(event)
