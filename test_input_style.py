#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输入框样式测试
Input Style Test
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_input_styles():
    """测试输入框样式"""
    print("=" * 60)
    print("🎨 输入框样式修复测试")
    print("=" * 60)
    
    try:
        from PyQt6.QtWidgets import QApplication, QLineEdit, QWidget, QVBoxLayout
        from ui.styles import GlassmorphismStyles
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建测试窗口
        window = QWidget()
        window.setWindowTitle("输入框样式测试")
        window.setFixedSize(400, 300)
        
        layout = QVBoxLayout(window)
        
        # 创建测试输入框
        input1 = QLineEdit()
        input1.setPlaceholderText("用户名输入框测试")
        input1.setFixedHeight(56)
        input1.setStyleSheet(GlassmorphismStyles.get_input_style())
        
        input2 = QLineEdit()
        input2.setPlaceholderText("密码输入框测试")
        input2.setFixedHeight(56)
        input2.setEchoMode(QLineEdit.EchoMode.Password)
        input2.setStyleSheet(GlassmorphismStyles.get_input_style())
        
        layout.addWidget(input1)
        layout.addWidget(input2)
        
        # 设置窗口背景
        window.setStyleSheet("""
        QWidget {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(30, 30, 60, 1),
                stop:0.5 rgba(60, 30, 90, 1),
                stop:1 rgba(90, 60, 120, 1));
        }
        """)
        
        print("✅ 输入框样式测试窗口创建成功")
        print("📋 修复内容:")
        print("  • 输入框高度增加到56px")
        print("  • 圆角半径统一为16px")
        print("  • 内边距增加到16px 20px")
        print("  • 添加焦点时的阴影效果")
        print("  • 确保文字完整显示")
        
        print("\n💡 测试说明:")
        print("  1. 两个输入框应该都是圆边样式")
        print("  2. 输入框高度足够显示完整文字")
        print("  3. 点击输入框时有紫色边框和阴影")
        print("  4. 占位符文字清晰可见")
        
        window.show()
        
        print("\n🖼️ 测试窗口已显示，请检查输入框样式")
        print("按Ctrl+C退出测试")
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_style_details():
    """显示样式详情"""
    print("\n" + "=" * 60)
    print("📝 输入框样式详情")
    print("=" * 60)
    
    try:
        from ui.styles import GlassmorphismStyles
        
        style = GlassmorphismStyles.get_input_style()
        print("🎨 当前输入框样式:")
        print(style)
        
        print("\n🔧 主要修改:")
        print("  • border-radius: 12px → 16px (更圆的边角)")
        print("  • padding: 12px 16px → 16px 20px (更大的内边距)")
        print("  • min-height: 24px (最小高度)")
        print("  • max-height: 48px (最大高度)")
        print("  • 新增焦点时的box-shadow效果")
        
        print("\n✨ 视觉效果:")
        print("  • 更圆润的边角设计")
        print("  • 更舒适的文字显示空间")
        print("  • 更明显的焦点反馈")
        print("  • 统一的玻璃态风格")
        
    except Exception as e:
        print(f"❌ 获取样式失败: {e}")

def main():
    """主函数"""
    print("🍽️ 智慧食堂管理系统 - 输入框样式修复")
    
    # 显示样式详情
    show_style_details()
    
    print("\n" + "=" * 60)
    print("🚀 启动样式测试")
    print("=" * 60)
    
    # 运行测试
    result = test_input_styles()
    
    if result:
        print("\n✅ 样式测试完成")
    else:
        print("\n❌ 样式测试失败")

if __name__ == "__main__":
    main()
