#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口数据清理测试脚本
Serial Data Cleaning Test Script
"""

import re

def clean_serial_data(data):
    """清理串口数据"""
    if not data:
        return ""
        
    # 移除所有空白字符
    data = data.strip()
    
    # 查找最后一个完整的重量数据模式
    pattern = r'[sw][gn]\d*\.?\d+kg'
    matches = re.findall(pattern, data, re.IGNORECASE)
    
    if matches:
        # 返回最后一个匹配的完整数据
        return matches[-1]
    
    return ""

def parse_weight_data(data):
    """解析重量数据"""
    # 清理数据
    cleaned_data = clean_serial_data(data)
    
    if not cleaned_data:
        return None, "无有效数据"
        
    # 使用正则表达式解析重量数据
    pattern = r'^[sw][gn](-?\d*\.?\d+)kg$'
    match = re.match(pattern, cleaned_data, re.IGNORECASE)
    
    if match:
        weight_str = match.group(1)
        try:
            weight_float = float(weight_str)
            weight = f"{weight_float:.2f}"
            return weight, f"成功: '{data}' -> '{cleaned_data}' -> {weight}kg"
        except ValueError:
            return None, f"格式错误: {weight_str}"
    else:
        return None, f"不匹配: '{data}' -> '{cleaned_data}'"

def test_serial_data_cleaning():
    """测试串口数据清理"""
    # 根据您提供的实际问题数据进行测试
    test_cases = [
        # 正常数据
        "wg0000.25kg",
        "sg0003.40kg",
        
        # 问题数据
        "wg0002.05kg.05kg",           # 数据重复粘连
        "wg00020002.95kg",            # 数据中间插入
        "wgwg0003.35kg",              # 前缀重复
        "kg",                         # 数据不完整
        "wg0003.4003.40kg",           # 数据中间重复
        "wg00wg0003.40kg",            # 前缀插入
        "sg0003.40kg3.40kg",          # 数据后面粘连
        "sg0003.40kg.40kg",           # 部分粘连
        "sg0003g0003.40kg",           # 中间插入字符
        "ssg0003.40kg",               # 前缀错误
        
        # 边界情况
        "",                           # 空数据
        "   ",                        # 空白数据
        "invalid_data",               # 完全无效
        "wg0000.00kg",                # 零重量
        "sg-12.34kg",                 # 负数
    ]
    
    print("🧪 串口数据清理测试")
    print("=" * 80)
    print(f"{'原始数据':<25} {'清理结果':<15} {'解析状态'}")
    print("-" * 80)
    
    success_count = 0
    total_count = len(test_cases)
    
    for data in test_cases:
        weight, status = parse_weight_data(data)
        
        # 显示结果
        cleaned = clean_serial_data(data)
        display_data = repr(data) if len(data) <= 20 else repr(data[:17] + "...")
        display_cleaned = cleaned if cleaned else "无"
        
        if weight:
            success_count += 1
            status_icon = "✅"
        else:
            status_icon = "❌"
        
        print(f"{display_data:<25} {display_cleaned:<15} {status_icon} {status}")
    
    print("-" * 80)
    print(f"📊 测试结果: {success_count}/{total_count} 成功解析")
    print(f"📈 成功率: {success_count/total_count*100:.1f}%")
    
    print("\n🔧 修复策略:")
    print("1. 数据缓冲: 累积数据直到找到完整包")
    print("2. 模式匹配: 提取最后一个有效的重量数据")
    print("3. 稳定性检查: 连续3次相同重量才发送")
    print("4. 错误过滤: 自动忽略格式错误的数据")

if __name__ == "__main__":
    test_serial_data_cleaning()
