#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试订单详情API修复
Test Order Details API Fix
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PyQt6.QtCore import QTimer
from ui.modules.order_module import OrderModule
from api.order_api import OrderAPI

class OrderDetailsFixTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("订单详情API修复测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置背景
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
            }
        """)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建测试按钮
        test_btn = QPushButton("测试订单详情功能")
        test_btn.setStyleSheet("""
            QPushButton {
                background: rgba(147, 51, 234, 0.3);
                color: white;
                border: 1px solid rgba(147, 51, 234, 0.5);
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
                margin: 10px;
            }
            QPushButton:hover {
                background: rgba(147, 51, 234, 0.5);
            }
        """)
        test_btn.clicked.connect(self.test_order_details)
        layout.addWidget(test_btn)
        
        # 创建订单模块
        self.order_module = OrderModule()
        
        # 创建并设置API
        self.order_api = OrderAPI("https://st.pcylsoft.com:9006/st/steelyard/")
        self.order_module.set_api(self.order_api)
        
        layout.addWidget(self.order_module)
        
        print("🔧 订单详情API修复测试")
        print("=" * 50)
        print("✅ 修复内容：")
        print("1. 修正了 self.order_api -> self.api 的属性名错误")
        print("2. 添加了API可用性检查")
        print("3. 确保API正确初始化后再调用详情功能")
        print("=" * 50)
        
        # 设置测试数据
        self.setup_test_data()
        
        # 5秒后自动关闭
        QTimer.singleShot(5000, self.close)
        
    def setup_test_data(self):
        """设置测试数据"""
        test_orders = [
            {
                'trade_no': 'TEST-ORDER-001',
                'circulate_name': '测试供应商A',
                'collect_time': '2023-12-01 10:30:00',
                'state': 0,
                'item_count': 2,
                'buy_quantity': '5',
                'deliver_quantity': '5',
                'goods': []
            },
            {
                'trade_no': 'TEST-ORDER-002',
                'circulate_name': '测试供应商B',
                'collect_time': '2023-12-01 14:15:00',
                'state': 1,
                'item_count': 3,
                'buy_quantity': '8',
                'deliver_quantity': '8',
                'goods': []
            }
        ]
        
        # 设置测试数据
        self.order_module.current_orders = test_orders
        self.order_module.populate_order_table(test_orders)
        print(f"📊 已加载 {len(test_orders)} 条测试订单")

    def test_order_details(self):
        """测试订单详情功能"""
        print("\n🧪 开始测试订单详情功能...")
        
        # 检查API是否正确设置
        if hasattr(self.order_module, 'api') and self.order_module.api:
            print("✅ OrderModule.api 已正确设置")
            
            # 检查API是否有get_order_details方法
            if hasattr(self.order_module.api, 'get_order_details'):
                print("✅ API具有get_order_details方法")
                
                # 模拟点击第一个订单
                if self.order_module.current_orders:
                    first_order = self.order_module.current_orders[0]
                    print(f"🔍 测试获取订单详情: {first_order['trade_no']}")
                    
                    try:
                        # 直接调用详情显示方法
                        self.order_module.show_order_details(first_order)
                        print("✅ 订单详情显示方法调用成功")
                        
                    except Exception as e:
                        print(f"❌ 订单详情显示失败: {e}")
                        
                else:
                    print("❌ 没有测试订单数据")
                    
            else:
                print("❌ API缺少get_order_details方法")
                
        else:
            print("❌ OrderModule.api 未正确设置")
        
        # 验证修复效果
        print("\n📋 修复验证结果：")
        
        # 1. 检查属性名修复
        try:
            # 尝试访问正确的属性
            api_instance = getattr(self.order_module, 'api', None)
            if api_instance:
                print("✅ self.api 属性访问正常")
            else:
                print("❌ self.api 属性为空")
                
        except AttributeError:
            print("❌ self.api 属性不存在")
        
        # 2. 检查API初始化
        if self.order_module.api and hasattr(self.order_module.api, 'get_order_details'):
            print("✅ API初始化和方法检查通过")
        else:
            print("❌ API初始化或方法检查失败")
        
        # 3. 检查错误处理
        print("✅ 添加了API可用性检查")
        print("✅ 添加了友好的错误提示")
        
        print("\n🎉 订单详情API修复测试完成！")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = OrderDetailsFixTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
