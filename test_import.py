#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入测试脚本
Import Test Script
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有导入"""
    print("=" * 50)
    print("🧪 导入测试")
    print("=" * 50)
    
    tests = [
        ("PyQt6基础组件", lambda: __import__('PyQt6.QtWidgets')),
        ("PyQt6核心", lambda: __import__('PyQt6.QtCore')),
        ("PyQt6GUI", lambda: __import__('PyQt6.QtGui')),
        ("requests", lambda: __import__('requests')),
        ("配置模块", lambda: __import__('config.settings')),
        ("样式模块", lambda: __import__('ui.styles')),
        ("API模块", lambda: __import__('api.auth_api')),
        ("认证管理器", lambda: __import__('utils.auth_manager')),
        ("登录窗口", lambda: __import__('ui.login_window')),
        ("主窗口", lambda: __import__('ui.main_window')),
    ]
    
    results = []
    
    for name, import_func in tests:
        try:
            import_func()
            print(f"✅ {name}")
            results.append(True)
        except Exception as e:
            print(f"❌ {name}: {e}")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 结果: {passed}/{total} 项导入成功")
    
    if passed == total:
        print("🎉 所有导入测试通过！")
        return True
    else:
        print("⚠️ 部分导入失败")
        return False

def test_specific_classes():
    """测试特定类的创建"""
    print("\n" + "=" * 50)
    print("🏗️ 类创建测试")
    print("=" * 50)
    
    try:
        from PyQt6.QtWidgets import QApplication, QDialog
        from ui.login_window import LoginWindow, AccountSelectionDialog, TokenVerifyThread
        from utils.auth_manager import AuthManager
        
        print("✅ 所有类导入成功")
        
        # 测试创建应用
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        print("✅ QApplication 创建成功")
        
        # 测试创建认证管理器
        auth_manager = AuthManager()
        print("✅ AuthManager 创建成功")
        
        # 测试创建登录窗口
        login_window = LoginWindow()
        print("✅ LoginWindow 创建成功")
        
        # 测试创建账号选择对话框
        dialog = AccountSelectionDialog(None, [])
        print("✅ AccountSelectionDialog 创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 类创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🍽️ 智慧食堂管理系统 - 导入修复验证")
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        # 测试类创建
        class_success = test_specific_classes()
        
        if class_success:
            print("\n🎉 所有测试通过！修复成功")
            print("💡 现在可以正常运行程序:")
            print("   python main.py")
            print("   python run.py")
        else:
            print("\n❌ 类创建测试失败")
    else:
        print("\n❌ 导入测试失败")

if __name__ == "__main__":
    main()
