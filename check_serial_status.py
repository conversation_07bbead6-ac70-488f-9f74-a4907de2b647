#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查串口状态
"""

def check_serial_ports():
    """检查串口端口"""
    try:
        import serial.tools.list_ports
        ports = list(serial.tools.list_ports.comports())
        
        print("🔍 可用串口:")
        for port in ports:
            print(f"  - {port.device}: {port.description}")
        
        return [port.device for port in ports]
    except Exception as e:
        print(f"❌ 检查串口失败: {e}")
        return []

def test_port_access():
    """测试端口访问"""
    ports = check_serial_ports()
    
    if not ports:
        print("❌ 没有发现串口")
        return
    
    for port in ports:
        try:
            import serial
            ser = serial.Serial(port, 9600, timeout=0.1)
            print(f"✅ 端口 {port} 可访问")
            ser.close()
        except Exception as e:
            print(f"❌ 端口 {port} 访问失败: {e}")

def main():
    print("🚀 串口状态检查")
    print("=" * 30)
    
    test_port_access()
    
    print("\n📝 修复说明:")
    print("1. ✅ 创建了全局串口管理器")
    print("2. ✅ 修改了订单管理模块使用全局管理器")
    print("3. ✅ 修改了出库管理模块使用全局管理器")
    print("4. ✅ 修改了系统设置测试连接功能")
    
    print("\n🔧 解决的问题:")
    print("- 串口资源冲突")
    print("- PermissionError(13, '拒绝访问')")
    print("- 多模块同时访问串口")
    
    print("\n🚀 预期效果:")
    print("- 订单管理模块启动时自动连接串口")
    print("- 出库管理模块显示共享连接状态")
    print("- 系统设置测试连接会智能处理冲突")
    print("- 所有模块共享重量数据")

if __name__ == "__main__":
    main()
