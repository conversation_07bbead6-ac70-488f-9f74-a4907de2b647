#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
玻璃态UI样式定义
Glassmorphism UI Styles Definition
"""

class GlassmorphismStyles:
    """玻璃态样式类"""
    
    # 主要颜色定义
    PRIMARY_COLOR = "rgba(147, 51, 234, 0.2)"
    PRIMARY_BORDER = "rgba(147, 51, 234, 0.3)"
    PRIMARY_HOVER = "rgba(147, 51, 234, 0.3)"
    PRIMARY_ACTIVE = "rgba(147, 51, 234, 0.4)"
    
    GLASS_LIGHT = "rgba(255, 255, 255, 0.1)"
    GLASS_MEDIUM = "rgba(255, 255, 255, 0.2)"
    GLASS_BORDER = "rgba(255, 255, 255, 0.2)"
    
    ERROR_COLOR = "rgba(239, 68, 68, 0.2)"
    ERROR_BORDER = "rgba(239, 68, 68, 0.5)"
    
    SUCCESS_COLOR = "rgba(34, 197, 94, 0.2)"
    SUCCESS_BORDER = "rgba(34, 197, 94, 0.5)"
    
    @staticmethod
    def get_main_window_style():
        """获取主窗口样式"""
        return """
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(30, 30, 60, 1),
                stop:0.5 rgba(60, 30, 90, 1),
                stop:1 rgba(90, 60, 120, 1));
        }
        """
    
    @staticmethod
    def get_glass_widget_style():
        """获取玻璃态组件基础样式"""
        return """
        QWidget {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
        }
        """
    
    @staticmethod
    def get_input_style():
        """获取输入框样式"""
        return """
        QLineEdit {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 15px 16px;
            color: white;
            font-size: 16px;
            font-weight: 500;
        }
        QLineEdit:focus {
            border: 1px solid rgba(147, 51, 234, 0.5);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 2px rgba(147, 51, 234, 0.2);
        }
        QLineEdit::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        """
    
    @staticmethod
    def get_button_style():
        """获取按钮样式"""
        return """
        QPushButton {
            background: rgba(147, 51, 234, 0.2);
            border: 1px solid rgba(147, 51, 234, 0.3);
            border-radius: 12px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
            font-size: 16px;
        }
        QPushButton:hover {
            background: rgba(147, 51, 234, 0.3);
            border: 1px solid rgba(147, 51, 234, 0.4);
        }
        QPushButton:pressed {
            background: rgba(147, 51, 234, 0.4);
        }
        QPushButton:disabled {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.3);
        }
        """
    
    @staticmethod
    def get_label_style():
        """获取标签样式"""
        return """
        QLabel {
            color: white;
            font-weight: 500;
            background: transparent;
            border: none;
        }
        """
    
    @staticmethod
    def get_card_style():
        """获取卡片样式"""
        return """
        QFrame {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
        }
        """
    
    @staticmethod
    def get_title_style():
        """获取标题样式"""
        return """
        QLabel {
            color: white;
            font-size: 32px;
            font-weight: 700;
            background: transparent;
            border: none;
        }
        """
    
    @staticmethod
    def get_subtitle_style():
        """获取副标题样式"""
        return """
        QLabel {
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            font-weight: 400;
            background: transparent;
            border: none;
        }
        """
