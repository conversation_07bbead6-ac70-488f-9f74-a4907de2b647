# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 收集数据文件
datas = []

# 添加配置文件
datas.append((os.path.join(project_root, 'config', '*.json'), 'config'))
datas.append((os.path.join(project_root, 'config', 'GrilmorphismUI.json'), 'config'))
datas.append((os.path.join(project_root, 'config', 'app_config.json'), 'config'))

# 添加photos目录（如果需要的话）
photos_dir = os.path.join(project_root, 'photos')
if os.path.exists(photos_dir):
    datas.append((photos_dir, 'photos'))

# 收集隐藏导入
hiddenimports = [
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'requests',
    'json',
    'os',
    'sys',
    'traceback',
    'datetime',
    'threading',
    'urllib.parse',
    'base64',
    'hashlib',
    'time',
]

# 收集PyQt6相关模块
hiddenimports.extend([
    'PyQt6.sip',
    'PyQt6.QtNetwork',
    'PyQt6.QtPrintSupport',
])

# 分析主程序
a = Analysis(
    ['main.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 创建PYZ文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='智慧食堂管理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 设置为False以隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    # icon='icon.ico',  # 如果有图标文件可以取消注释
)

# 创建分发目录（可选，用于创建目录版本）
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='智慧食堂管理系统_目录版',
)
