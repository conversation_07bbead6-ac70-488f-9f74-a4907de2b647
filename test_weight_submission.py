#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重量提交功能测试脚本
Test script for weight submission functionality
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QMessageBox
from PyQt6.QtCore import Qt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_weight_submission_module():
    """测试重量提交模块"""
    try:
        from ui.modules.weight_submission_module import WeightSubmissionModule
        from api.weight_api import WeightAPI
        
        print("✅ 重量提交模块导入成功")
        
        # 创建测试应用
        app = QApplication(sys.argv)
        
        # 创建主窗口
        window = QMainWindow()
        window.setWindowTitle("重量提交功能测试")
        window.setGeometry(100, 100, 800, 600)
        
        # 创建重量提交模块
        weight_module = WeightSubmissionModule()
        
        # 创建API实例（测试用）
        weight_api = WeightAPI("https://st.pcylsoft.com:9006/st/steelyard/")
        weight_module.set_api(weight_api)
        
        # 设置测试商品数据（使用正确的API响应格式）
        test_product = {
            'id': 194933,  # 商品ID（用于重量提交API）
            'name': '菠萝',  # 商品名称
            'code': '13010501',  # 商品编码
            'stock_mode': 1,  # 入库模式：1=入库
            'buy_quantity': 100,
            'buy_unit': 'kg',
            'deliver_quantity': 95,
            'deliver_unit': 'kg',
            'receive_quantity': '未收货',
            'stock_quantity': 0,
            'spec': '规格测试',
            'receive_date': '2024-01-15',
            'batch': 'BATCH001'
        }
        
        weight_module.set_product_info(test_product)
        
        # 设置为中央组件
        window.setCentralWidget(weight_module)
        
        # 设置样式
        window.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a1a2e, stop:0.5 #16213e, stop:1 #0f3460);
            }
        """)
        
        print("✅ 重量提交界面创建成功")
        print("📝 测试说明:")
        print("   1. 界面应显示商品信息")
        print("   2. 串口连接状态应显示为红色（未连接）")
        print("   3. 可以手动添加重量数据进行测试")
        print("   4. 提交按钮应在有重量数据时启用")
        
        window.show()
        return app.exec()
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("请确保所有依赖模块都已正确创建")
        return 1
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return 1

def test_serial_dependency():
    """测试串口依赖"""
    try:
        import serial
        print("✅ pyserial 库已安装")
        
        # 测试串口列表
        import serial.tools.list_ports
        ports = list(serial.tools.list_ports.comports())
        print(f"📡 可用串口: {len(ports)} 个")
        for port in ports:
            print(f"   - {port.device}: {port.description}")
            
        return True
    except ImportError:
        print("❌ pyserial 库未安装")
        print("请运行: pip install pyserial")
        return False

def test_weight_api():
    """测试重量API"""
    try:
        from api.weight_api import WeightAPI
        
        api = WeightAPI("https://st.pcylsoft.com:9006/st/steelyard/")
        
        # 测试数据验证
        validation_result = api.validate_weight_data(
            id=1058706,
            quantity="23.5,24.2,25.1",
            stock_mode=0
        )
        
        print("✅ 重量API创建成功")
        print(f"📊 数据验证测试: {validation_result}")
        
        # 测试重量格式化
        weights = [23.5, 24.2, 25.1]
        formatted = api.format_weight_quantity(weights)
        print(f"📝 重量格式化: {weights} -> '{formatted}'")
        
        # 测试重量解析
        parsed = api.parse_weight_quantity(formatted)
        print(f"🔍 重量解析: '{formatted}' -> {parsed}")
        
        return True
    except Exception as e:
        print(f"❌ 重量API测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始重量提交功能测试")
    print("=" * 50)

    # 测试串口依赖
    print("\n1. 测试串口依赖...")
    serial_ok = test_serial_dependency()

    # 测试重量API
    print("\n2. 测试重量API...")
    api_ok = test_weight_api()

    print(f"\n📊 测试结果:")
    print(f"   串口依赖: {'✅' if serial_ok else '❌'}")
    print(f"   重量API: {'✅' if api_ok else '❌'}")

    if serial_ok and api_ok:
        print("\n✅ 所有基础功能测试通过！")
        print("💡 提示: 可以运行主程序测试完整功能")
        return 0
    else:
        print("\n❌ 部分功能测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
