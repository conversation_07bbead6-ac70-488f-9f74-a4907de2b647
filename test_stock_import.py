print("Starting stock API test...")

try:
    print("Step 1: Testing basic imports")
    import json
    import requests
    print("✅ Basic imports successful")
    
    print("Step 2: Testing typing imports")
    from typing import Dict, Any, List
    print("✅ Typing imports successful")
    
    print("Step 3: Testing auth_api import")
    from api.auth_api import AuthAPI
    print("✅ AuthAPI import successful")
    
    print("Step 4: Testing stock_api import")
    from api.stock_api import StockAPI
    print("✅ StockAPI import successful")
    
    print("Step 5: Creating StockAPI instance")
    api = StockAPI("https://test.com")
    print("✅ StockAPI instance created")
    
    print("🎉 All tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
