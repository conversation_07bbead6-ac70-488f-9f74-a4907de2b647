#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复脚本
Verify Fix Script
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    print("验证QDialog导入修复...")
    
    try:
        # 测试基本导入
        from PyQt6.QtWidgets import QDialog
        print("✅ QDialog导入成功")
        
        # 测试登录窗口导入
        from ui.login_window import AccountSelectionDialog
        print("✅ AccountSelectionDialog导入成功")
        
        # 测试创建对话框
        dialog = AccountSelectionDialog(None, [])
        print("✅ AccountSelectionDialog创建成功")
        
        print("\n🎉 修复验证成功！")
        print("现在可以正常运行程序了:")
        print("  python main.py")
        print("  python run.py")
        print("  python start_fixed.py")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
