#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
入库管理模块
Stock In Management Module
"""

import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFrame, QLabel, 
    QPushButton, QLineEdit, QComboBox, QTableWidget, QTableWidgetItem,
    QHeaderView, QSplitter, QTabWidget, QTextEdit, QProgressBar,
    QMessageBox, QSpinBox, QDoubleSpinBox, QCheckBox, QGroupBox,
    QScrollArea, QSizePolicy
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer, QMutex
from PyQt6.QtGui import QFont, QPixmap, QIcon

# 尝试导入摄像头模块
try:
    from .camera_module import CameraModule
except ImportError:
    CameraModule = None
    print("⚠️ 摄像头模块不可用，请安装opencv-python")

# 导入串口管理器
try:
    from ..serial_manager import SerialManager
except ImportError:
    SerialManager = None
    print("⚠️ 串口管理器不可用")


class StockInDataLoader(QThread):
    """入库数据加载线程"""
    
    data_loaded = pyqtSignal(str, dict)  # 数据类型, 数据
    error_occurred = pyqtSignal(str, str)  # 数据类型, 错误信息
    
    def __init__(self, api, data_type: str):
        super().__init__()
        self.api = api
        self.data_type = data_type
        
    def run(self):
        """运行数据加载"""
        try:
            if self.data_type == "products":
                result = self.api.get_products_list()
            elif self.data_type == "companies":
                result = self.api.get_companies_list()
            elif self.data_type == "depots":
                result = self.api.get_depot_list()
            else:
                self.error_occurred.emit(self.data_type, f"未知数据类型: {self.data_type}")
                return
                
            if result.get('code') == 200:
                self.data_loaded.emit(self.data_type, result)
            else:
                self.error_occurred.emit(self.data_type, result.get('msg', '加载失败'))
                
        except Exception as e:
            self.error_occurred.emit(self.data_type, str(e))


class StockInSubmitter(QThread):
    """入库提交线程"""
    
    submit_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    progress_updated = pyqtSignal(int)
    
    def __init__(self, api, submit_data: dict):
        super().__init__()
        self.api = api
        self.submit_data = submit_data
        
    def run(self):
        """运行提交"""
        try:
            self.progress_updated.emit(10)
            
            # 获取入库单号
            index_result = self.api.get_stock_in_index()
            if index_result.get('code') != 200:
                self.error_occurred.emit(f"获取入库单号失败: {index_result.get('msg', '未知错误')}")
                return
                
            stock_in_code = index_result['msg']
            self.progress_updated.emit(30)
            
            # 提交入库申请
            result = self.api.submit_stock_in(
                code=stock_in_code,
                datetime=self.submit_data['datetime'],
                depot_code=self.submit_data['depot_code'],
                company_code=self.submit_data['company_code'],
                details=self.submit_data['details']
            )
            
            self.progress_updated.emit(100)
            
            if result.get('code') == 200:
                result['stock_in_code'] = stock_in_code
                self.submit_completed.emit(result)
            else:
                self.error_occurred.emit(result.get('msg', '提交失败'))
                
        except Exception as e:
            self.error_occurred.emit(str(e))


class StockInModule(QWidget):
    """入库管理模块"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.api = None
        
        # 数据存储
        self.products_data = []
        self.companies_data = []
        self.depots_data = []
        self.selected_items = []  # 选中的商品项目
        
        # 线程管理
        self.loader_threads = {}
        self.submit_thread = None
        
        # 串口和摄像头
        self.serial_manager = None
        self.camera_module = None
        self.current_weight = "0.0"
        self.captured_photos = []
        self.serial_enabled = False  # 串口开关状态
        
        self.init_ui()
        self.apply_styles()
        self.setup_connections()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 创建标题栏
        self.create_header(layout)
        
        # 创建主要内容区域
        self.create_main_content(layout)
        
        # 创建状态栏
        self.create_status_bar(layout)
        
    def create_header(self, parent_layout):
        """创建标题栏"""
        header = QFrame()
        header.setObjectName("headerFrame")
        header.setFixedHeight(80)
        
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(20, 15, 20, 15)
        
        # 标题
        title_label = QLabel("📦 入库管理")
        title_label.setObjectName("titleLabel")
        title_label.setFont(QFont("Inter", 18, QFont.Weight.Bold))
        
        # 串口开关
        self.serial_switch = QCheckBox("启用串口")
        self.serial_switch.setObjectName("serialSwitch")
        self.serial_switch.toggled.connect(self.toggle_serial)
        
        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新数据")
        self.refresh_btn.setObjectName("refreshButton")
        self.refresh_btn.clicked.connect(self.refresh_data)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.serial_switch)
        header_layout.addWidget(self.refresh_btn)
        
        parent_layout.addWidget(header)
        
    def create_main_content(self, parent_layout):
        """创建主要内容区域"""
        # 使用分割器创建左右布局
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：选择和配置区域
        left_widget = self.create_selection_area()
        splitter.addWidget(left_widget)
        
        # 右侧：商品和操作区域
        right_widget = self.create_operation_area()
        splitter.addWidget(right_widget)
        
        # 设置分割比例
        splitter.setSizes([400, 600])
        
        parent_layout.addWidget(splitter)
        
    def create_selection_area(self):
        """创建选择区域"""
        widget = QFrame()
        widget.setObjectName("selectionFrame")
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # 基础信息选择
        self.create_basic_selection(layout)
        
        # 商品列表
        self.create_product_list(layout)
        
        return widget
        
    def create_basic_selection(self, parent_layout):
        """创建基础信息选择"""
        group = QGroupBox("基础信息")
        group.setObjectName("basicGroup")
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # 仓库选择
        layout.addWidget(QLabel("仓库:"), 0, 0)
        self.depot_combo = QComboBox()
        self.depot_combo.setObjectName("depotCombo")
        layout.addWidget(self.depot_combo, 0, 1)
        
        # 供应商选择
        layout.addWidget(QLabel("供应商:"), 1, 0)
        self.company_combo = QComboBox()
        self.company_combo.setObjectName("companyCombo")
        layout.addWidget(self.company_combo, 1, 1)
        
        # 入库日期
        layout.addWidget(QLabel("入库日期:"), 2, 0)
        self.date_edit = QLineEdit()
        self.date_edit.setObjectName("dateEdit")
        self.date_edit.setText(datetime.now().strftime("%Y-%m-%d"))
        self.date_edit.setPlaceholderText("YYYY-MM-DD")
        layout.addWidget(self.date_edit, 2, 1)
        
        parent_layout.addWidget(group)
        
    def create_product_list(self, parent_layout):
        """创建商品列表"""
        group = QGroupBox("商品选择")
        group.setObjectName("productGroup")
        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        
        # 搜索框
        self.product_search = QLineEdit()
        self.product_search.setObjectName("productSearch")
        self.product_search.setPlaceholderText("搜索商品...")
        self.product_search.textChanged.connect(self.filter_products)
        layout.addWidget(self.product_search)
        
        # 商品表格
        self.product_table = QTableWidget()
        self.product_table.setObjectName("productTable")
        self.product_table.setColumnCount(3)
        self.product_table.setHorizontalHeaderLabels(["代码", "名称", "单位"])
        self.product_table.horizontalHeader().setStretchLastSection(True)
        self.product_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.product_table.itemDoubleClicked.connect(self.add_product_to_list)
        layout.addWidget(self.product_table)
        
        parent_layout.addWidget(group)
        
    def create_operation_area(self):
        """创建操作区域"""
        widget = QFrame()
        widget.setObjectName("operationFrame")
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # 创建标签页
        tab_widget = QTabWidget()
        tab_widget.setObjectName("operationTabs")
        
        # 商品明细标签页
        tab_widget.addTab(self.create_items_tab(), "📋 商品明细")
        
        # 重量录入标签页
        tab_widget.addTab(self.create_weight_tab(), "⚖️ 重量录入")
        
        # 照片拍摄标签页
        tab_widget.addTab(self.create_photo_tab(), "📸 照片拍摄")
        
        layout.addWidget(tab_widget)
        
        # 提交按钮
        self.create_submit_section(layout)
        
        return widget

    def create_items_tab(self):
        """创建商品明细标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # 已选商品列表
        self.items_table = QTableWidget()
        self.items_table.setObjectName("itemsTable")
        self.items_table.setColumnCount(6)
        self.items_table.setHorizontalHeaderLabels(["商品代码", "商品名称", "数量", "单位", "价格", "操作"])
        self.items_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.items_table)

        # 操作按钮
        button_layout = QHBoxLayout()

        self.add_item_btn = QPushButton("➕ 添加商品")
        self.add_item_btn.setObjectName("addItemButton")
        self.add_item_btn.clicked.connect(self.show_add_item_dialog)

        self.remove_item_btn = QPushButton("➖ 移除商品")
        self.remove_item_btn.setObjectName("removeItemButton")
        self.remove_item_btn.clicked.connect(self.remove_selected_item)
        self.remove_item_btn.setEnabled(False)

        self.clear_items_btn = QPushButton("🗑️ 清空列表")
        self.clear_items_btn.setObjectName("clearItemsButton")
        self.clear_items_btn.clicked.connect(self.clear_items_list)

        button_layout.addWidget(self.add_item_btn)
        button_layout.addWidget(self.remove_item_btn)
        button_layout.addWidget(self.clear_items_btn)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        return widget

    def create_weight_tab(self):
        """创建重量录入标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # 串口状态显示
        status_group = QGroupBox("串口状态")
        status_group.setObjectName("statusGroup")
        status_layout = QVBoxLayout(status_group)

        self.serial_status_label = QLabel("🔴 串口未连接")
        self.serial_status_label.setObjectName("serialStatusLabel")
        status_layout.addWidget(self.serial_status_label)

        layout.addWidget(status_group)

        # 当前重量显示
        weight_group = QGroupBox("当前重量")
        weight_group.setObjectName("weightGroup")
        weight_layout = QVBoxLayout(weight_group)

        self.current_weight_label = QLabel("0.0 kg")
        self.current_weight_label.setObjectName("currentWeightLabel")
        self.current_weight_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_weight_label.setFont(QFont("Inter", 24, QFont.Weight.Bold))
        weight_layout.addWidget(self.current_weight_label)

        # 手动输入重量
        manual_layout = QHBoxLayout()
        manual_layout.addWidget(QLabel("手动输入:"))

        self.manual_weight_input = QDoubleSpinBox()
        self.manual_weight_input.setObjectName("manualWeightInput")
        self.manual_weight_input.setRange(0.0, 9999.99)
        self.manual_weight_input.setDecimals(2)
        self.manual_weight_input.setSuffix(" kg")
        manual_layout.addWidget(self.manual_weight_input)

        self.apply_weight_btn = QPushButton("应用重量")
        self.apply_weight_btn.setObjectName("applyWeightButton")
        self.apply_weight_btn.clicked.connect(self.apply_manual_weight)
        manual_layout.addWidget(self.apply_weight_btn)

        weight_layout.addLayout(manual_layout)
        layout.addWidget(weight_group)

        # 重量记录
        record_group = QGroupBox("重量记录")
        record_group.setObjectName("recordGroup")
        record_layout = QVBoxLayout(record_group)

        self.weight_list = QTextEdit()
        self.weight_list.setObjectName("weightList")
        self.weight_list.setMaximumHeight(150)
        self.weight_list.setReadOnly(True)
        record_layout.addWidget(self.weight_list)

        layout.addWidget(record_group)
        layout.addStretch()

        return widget

    def create_photo_tab(self):
        """创建照片拍摄标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # 摄像头预览
        if CameraModule:
            self.camera_module = CameraModule()
            self.camera_module.photo_captured.connect(self.on_photo_captured)
            layout.addWidget(self.camera_module)
        else:
            no_camera_label = QLabel("📷\n摄像头模块不可用\n请安装opencv-python")
            no_camera_label.setObjectName("noCameraLabel")
            no_camera_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            no_camera_label.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.5);
                    font-size: 14px;
                    padding: 40px;
                    min-height: 200px;
                }
            """)
            layout.addWidget(no_camera_label)
            self.camera_module = None

        # 照片列表
        photos_group = QGroupBox("已拍摄照片")
        photos_group.setObjectName("photosGroup")
        photos_layout = QVBoxLayout(photos_group)

        self.photos_list = QTextEdit()
        self.photos_list.setObjectName("photosList")
        self.photos_list.setMaximumHeight(100)
        self.photos_list.setReadOnly(True)
        photos_layout.addWidget(self.photos_list)

        layout.addWidget(photos_group)

        return widget

    def create_submit_section(self, parent_layout):
        """创建提交区域"""
        submit_frame = QFrame()
        submit_frame.setObjectName("submitFrame")
        submit_layout = QVBoxLayout(submit_frame)
        submit_layout.setSpacing(10)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progressBar")
        self.progress_bar.setVisible(False)
        submit_layout.addWidget(self.progress_bar)

        # 提交按钮
        button_layout = QHBoxLayout()

        self.submit_btn = QPushButton("📤 提交入库申请")
        self.submit_btn.setObjectName("submitButton")
        self.submit_btn.clicked.connect(self.submit_stock_in)
        self.submit_btn.setEnabled(False)

        self.reset_btn = QPushButton("🔄 重置表单")
        self.reset_btn.setObjectName("resetButton")
        self.reset_btn.clicked.connect(self.reset_form)

        button_layout.addWidget(self.submit_btn)
        button_layout.addWidget(self.reset_btn)
        button_layout.addStretch()

        submit_layout.addLayout(button_layout)
        parent_layout.addWidget(submit_frame)

    def create_status_bar(self, parent_layout):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setObjectName("statusFrame")
        status_frame.setFixedHeight(40)

        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(20, 5, 20, 5)

        self.status_label = QLabel("就绪")
        self.status_label.setObjectName("statusLabel")

        status_layout.addWidget(self.status_label)
        status_layout.addStretch()

        parent_layout.addWidget(status_frame)

    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            /* 主框架样式 */
            QFrame#headerFrame, QFrame#selectionFrame, QFrame#operationFrame,
            QFrame#submitFrame, QFrame#statusFrame {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 16px;
            }

            /* 标题样式 */
            QLabel#titleLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
            }

            /* 组框样式 */
            QGroupBox {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                padding-top: 15px;
                margin-top: 10px;
                color: white;
                font-weight: 500;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: rgba(255, 255, 255, 0.9);
            }

            /* 输入框样式 */
            QLineEdit, QComboBox, QDoubleSpinBox {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 8px 12px;
                color: white;
                font-size: 14px;
                min-height: 20px;
            }

            QLineEdit:focus, QComboBox:focus, QDoubleSpinBox:focus {
                border: 1px solid rgba(147, 51, 234, 0.5);
                background: rgba(255, 255, 255, 0.15);
            }

            QLineEdit::placeholder {
                color: rgba(255, 255, 255, 0.5);
            }

            /* 按钮样式 */
            QPushButton {
                background: rgba(147, 51, 234, 0.2);
                border: 1px solid rgba(147, 51, 234, 0.3);
                border-radius: 8px;
                padding: 8px 16px;
                color: white;
                font-size: 14px;
                font-weight: 500;
                min-height: 20px;
            }

            QPushButton:hover {
                background: rgba(147, 51, 234, 0.3);
                border: 1px solid rgba(147, 51, 234, 0.4);
            }

            QPushButton:pressed {
                background: rgba(147, 51, 234, 0.4);
            }

            QPushButton:disabled {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                color: rgba(255, 255, 255, 0.3);
            }

            /* 特殊按钮样式 */
            QPushButton#submitButton {
                background: rgba(34, 197, 94, 0.2);
                border: 1px solid rgba(34, 197, 94, 0.3);
            }

            QPushButton#submitButton:hover {
                background: rgba(34, 197, 94, 0.3);
            }

            QPushButton#resetButton {
                background: rgba(239, 68, 68, 0.2);
                border: 1px solid rgba(239, 68, 68, 0.3);
            }

            QPushButton#resetButton:hover {
                background: rgba(239, 68, 68, 0.3);
            }

            /* 表格样式 */
            QTableWidget {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                gridline-color: rgba(255, 255, 255, 0.1);
                color: white;
            }

            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            QTableWidget::item:selected {
                background: rgba(147, 51, 234, 0.3);
            }

            QHeaderView::section {
                background: rgba(255, 255, 255, 0.1);
                border: none;
                padding: 8px;
                color: white;
                font-weight: 600;
            }

            /* 标签页样式 */
            QTabWidget::pane {
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.05);
            }

            QTabBar::tab {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                padding: 8px 16px;
                margin-right: 2px;
                color: rgba(255, 255, 255, 0.7);
            }

            QTabBar::tab:selected {
                background: rgba(147, 51, 234, 0.3);
                color: white;
            }

            QTabBar::tab:first {
                border-top-left-radius: 8px;
            }

            QTabBar::tab:last {
                border-top-right-radius: 8px;
            }

            /* 文本编辑框样式 */
            QTextEdit {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                padding: 8px;
                color: white;
                font-family: 'Consolas', monospace;
                font-size: 12px;
            }

            /* 进度条样式 */
            QProgressBar {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                text-align: center;
                color: white;
            }

            QProgressBar::chunk {
                background: linear-gradient(90deg, rgba(147, 51, 234, 0.8), rgba(59, 130, 246, 0.8));
                border-radius: 8px;
            }

            /* 复选框样式 */
            QCheckBox {
                color: white;
                font-size: 14px;
                spacing: 8px;
            }

            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                background: rgba(255, 255, 255, 0.1);
            }

            QCheckBox::indicator:checked {
                background: rgba(147, 51, 234, 0.6);
                border: 1px solid rgba(147, 51, 234, 0.8);
            }

            /* 状态标签样式 */
            QLabel#statusLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 12px;
            }

            QLabel#currentWeightLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                background: rgba(59, 130, 246, 0.1);
                border: 1px solid rgba(59, 130, 246, 0.3);
                border-radius: 12px;
                padding: 20px;
            }

            QLabel#serialStatusLabel {
                font-size: 14px;
                padding: 8px;
                border-radius: 6px;
            }
        """)

    def setup_connections(self):
        """设置信号连接"""
        # 表格选择变化
        self.items_table.itemSelectionChanged.connect(self.on_items_selection_changed)

        # 检查提交按钮状态
        self.depot_combo.currentTextChanged.connect(self.check_submit_enabled)
        self.company_combo.currentTextChanged.connect(self.check_submit_enabled)

    def set_api(self, api):
        """设置API实例"""
        self.api = api
        if api:
            self.refresh_data()

    def refresh_data(self):
        """刷新数据"""
        if not self.api:
            self.status_label.setText("❌ API未初始化")
            return

        self.status_label.setText("正在加载数据...")

        # 加载仓库数据
        self.load_data("depots")

        # 加载供应商数据
        self.load_data("companies")

        # 加载商品数据
        self.load_data("products")

    def load_data(self, data_type: str):
        """加载数据"""
        if data_type in self.loader_threads:
            return  # 已在加载中

        loader = StockInDataLoader(self.api, data_type)
        loader.data_loaded.connect(self.on_data_loaded)
        loader.error_occurred.connect(self.on_data_error)
        loader.finished.connect(lambda: self.loader_threads.pop(data_type, None))

        self.loader_threads[data_type] = loader
        loader.start()

    def on_data_loaded(self, data_type: str, result: dict):
        """数据加载完成"""
        data = result.get('data', [])

        if data_type == "depots":
            self.depots_data = data
            self.update_depot_combo()
        elif data_type == "companies":
            self.companies_data = data
            self.update_company_combo()
        elif data_type == "products":
            self.products_data = data
            self.update_product_table()

        self.status_label.setText(f"✅ {data_type} 数据加载完成 ({len(data)} 条)")

    def on_data_error(self, data_type: str, error_msg: str):
        """数据加载错误"""
        self.status_label.setText(f"❌ {data_type} 数据加载失败: {error_msg}")

    def update_depot_combo(self):
        """更新仓库下拉框"""
        self.depot_combo.clear()
        self.depot_combo.addItem("请选择仓库", "")

        for depot in self.depots_data:
            name = depot.get('name', '')
            code = depot.get('code', '')
            self.depot_combo.addItem(f"{name} ({code})", code)

    def update_company_combo(self):
        """更新供应商下拉框"""
        self.company_combo.clear()
        self.company_combo.addItem("请选择供应商", "")

        for company in self.companies_data:
            name = company.get('name', '')
            code = company.get('code', '')
            self.company_combo.addItem(f"{name} ({code})", code)

    def update_product_table(self):
        """更新商品表格"""
        self.product_table.setRowCount(len(self.products_data))

        for row, product in enumerate(self.products_data):
            code_item = QTableWidgetItem(product.get('code', ''))
            name_item = QTableWidgetItem(product.get('name', ''))
            unit_item = QTableWidgetItem(product.get('unit', ''))

            self.product_table.setItem(row, 0, code_item)
            self.product_table.setItem(row, 1, name_item)
            self.product_table.setItem(row, 2, unit_item)

    def filter_products(self):
        """过滤商品"""
        search_text = self.product_search.text().lower()

        for row in range(self.product_table.rowCount()):
            show_row = False

            for col in range(self.product_table.columnCount()):
                item = self.product_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break

            self.product_table.setRowHidden(row, not show_row)

    def add_product_to_list(self, item):
        """双击添加商品到列表"""
        row = item.row()
        if row < len(self.products_data):
            product = self.products_data[row]
            self.show_add_item_dialog(product)

    def show_add_item_dialog(self, product=None):
        """显示添加商品对话框"""
        from PyQt6.QtWidgets import QDialog, QFormLayout, QDialogButtonBox

        dialog = QDialog(self)
        dialog.setWindowTitle("添加商品")
        dialog.setModal(True)
        dialog.resize(400, 300)

        layout = QFormLayout(dialog)

        # 商品选择
        product_combo = QComboBox()
        for p in self.products_data:
            product_combo.addItem(f"{p.get('name', '')} ({p.get('code', '')})", p)

        if product:
            # 设置选中的商品
            for i in range(product_combo.count()):
                if product_combo.itemData(i).get('code') == product.get('code'):
                    product_combo.setCurrentIndex(i)
                    break

        layout.addRow("商品:", product_combo)

        # 数量输入
        quantity_input = QDoubleSpinBox()
        quantity_input.setRange(0.01, 9999.99)
        quantity_input.setDecimals(2)
        quantity_input.setValue(1.0)
        layout.addRow("数量:", quantity_input)

        # 价格输入
        price_input = QDoubleSpinBox()
        price_input.setRange(0.01, 99999.99)
        price_input.setDecimals(2)
        price_input.setValue(1.0)
        layout.addRow("价格:", price_input)

        # 按钮
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addRow(buttons)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            selected_product = product_combo.currentData()
            quantity = quantity_input.value()
            price = price_input.value()

            self.add_item_to_table(selected_product, quantity, price)

    def add_item_to_table(self, product: dict, quantity: float, price: float):
        """添加商品到表格"""
        row = self.items_table.rowCount()
        self.items_table.insertRow(row)

        # 设置商品信息
        code_item = QTableWidgetItem(product.get('code', ''))
        name_item = QTableWidgetItem(product.get('name', ''))
        quantity_item = QTableWidgetItem(str(quantity))
        unit_item = QTableWidgetItem(product.get('unit', ''))
        price_item = QTableWidgetItem(str(price))

        # 删除按钮
        remove_btn = QPushButton("删除")
        remove_btn.setObjectName("removeButton")
        remove_btn.clicked.connect(lambda: self.remove_item_by_row(row))

        self.items_table.setItem(row, 0, code_item)
        self.items_table.setItem(row, 1, name_item)
        self.items_table.setItem(row, 2, quantity_item)
        self.items_table.setItem(row, 3, unit_item)
        self.items_table.setItem(row, 4, price_item)
        self.items_table.setCellWidget(row, 5, remove_btn)

        # 保存商品数据
        item_data = {
            'product': product,
            'quantity': quantity,
            'price': price
        }
        self.selected_items.append(item_data)

        self.check_submit_enabled()

    def remove_item_by_row(self, row: int):
        """根据行号删除商品"""
        if 0 <= row < len(self.selected_items):
            self.selected_items.pop(row)
            self.items_table.removeRow(row)

            # 重新设置删除按钮的行号
            for i in range(self.items_table.rowCount()):
                btn = self.items_table.cellWidget(i, 5)
                if btn:
                    btn.clicked.disconnect()
                    btn.clicked.connect(lambda checked, r=i: self.remove_item_by_row(r))

            self.check_submit_enabled()

    def remove_selected_item(self):
        """删除选中的商品"""
        current_row = self.items_table.currentRow()
        if current_row >= 0:
            self.remove_item_by_row(current_row)

    def clear_items_list(self):
        """清空商品列表"""
        self.items_table.setRowCount(0)
        self.selected_items.clear()
        self.check_submit_enabled()

    def on_items_selection_changed(self):
        """商品选择变化"""
        has_selection = self.items_table.currentRow() >= 0
        self.remove_item_btn.setEnabled(has_selection)

    def toggle_serial(self, enabled: bool):
        """切换串口状态"""
        self.serial_enabled = enabled

        if enabled:
            self.start_serial_connection()
        else:
            self.stop_serial_connection()

    def start_serial_connection(self):
        """启动串口连接"""
        try:
            # 使用全局串口管理器
            from ..global_serial_manager import GlobalSerialManager
            self.serial_manager = GlobalSerialManager()

            # 添加订阅者
            callbacks = {
                'weight_received': self.on_weight_data_received,
                'connection_status': self.on_serial_connection_status,
                'error_occurred': self.on_serial_error
            }

            self.serial_manager.add_subscriber("stock_in_module", callbacks)

            # 启动连接
            if self.serial_manager.start_connection():
                self.serial_status_label.setText("🟡 正在连接串口...")
                self.serial_status_label.setStyleSheet("color: #eab308;")
            else:
                self.serial_status_label.setText("❌ 串口连接失败")
                self.serial_status_label.setStyleSheet("color: #ef4444;")

        except Exception as e:
            self.serial_status_label.setText(f"❌ 串口连接失败: {str(e)}")
            self.serial_status_label.setStyleSheet("color: #ef4444;")

    def stop_serial_connection(self):
        """停止串口连接"""
        if self.serial_manager:
            try:
                self.serial_manager.remove_subscriber("stock_in_module")
                self.serial_manager = None
            except:
                pass

        self.serial_status_label.setText("🔴 串口未连接")
        self.serial_status_label.setStyleSheet("color: #ef4444;")

    def on_serial_connection_status(self, connected: bool):
        """串口连接状态变化"""
        if connected:
            self.serial_status_label.setText("🟢 串口已连接")
            self.serial_status_label.setStyleSheet("color: #22c55e;")
        else:
            self.serial_status_label.setText("🔴 串口已断开")
            self.serial_status_label.setStyleSheet("color: #ef4444;")

    def on_serial_error(self, error_msg: str):
        """串口错误"""
        self.serial_status_label.setText(f"❌ 串口错误: {error_msg}")
        self.serial_status_label.setStyleSheet("color: #ef4444;")

    def on_weight_data_received(self, weight_data: str):
        """接收到重量数据"""
        try:
            # 解析重量数据
            import re
            pattern = r'[sw][gn](-?\d+\.?\d*)kg'
            match = re.search(pattern, weight_data.lower())

            if match:
                weight = float(match.group(1))
                self.current_weight = str(weight)
                self.current_weight_label.setText(f"{weight:.2f} kg")

                # 记录重量
                timestamp = datetime.now().strftime("%H:%M:%S")
                record = f"[{timestamp}] {weight:.2f} kg\n"
                self.weight_list.append(record)

                # 自动滚动到底部
                cursor = self.weight_list.textCursor()
                cursor.movePosition(cursor.MoveOperation.End)
                self.weight_list.setTextCursor(cursor)

        except Exception as e:
            print(f"解析重量数据失败: {e}")

    def apply_manual_weight(self):
        """应用手动输入的重量"""
        weight = self.manual_weight_input.value()
        self.current_weight = str(weight)
        self.current_weight_label.setText(f"{weight:.2f} kg")

        # 记录重量
        timestamp = datetime.now().strftime("%H:%M:%S")
        record = f"[{timestamp}] {weight:.2f} kg (手动输入)\n"
        self.weight_list.append(record)

        # 自动滚动到底部
        cursor = self.weight_list.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.weight_list.setTextCursor(cursor)

    def on_photo_captured(self, photo_path: str):
        """照片拍摄完成"""
        self.captured_photos.append(photo_path)

        # 更新照片列表显示
        photo_name = os.path.basename(photo_path)
        timestamp = datetime.now().strftime("%H:%M:%S")
        record = f"[{timestamp}] {photo_name}\n"
        self.photos_list.append(record)

        # 自动滚动到底部
        cursor = self.photos_list.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.photos_list.setTextCursor(cursor)

    def check_submit_enabled(self):
        """检查是否可以提交"""
        can_submit = (
            self.depot_combo.currentData() and
            self.company_combo.currentData() and
            len(self.selected_items) > 0 and
            self.date_edit.text().strip()
        )

        self.submit_btn.setEnabled(can_submit)

    def submit_stock_in(self):
        """提交入库申请"""
        if not self.api:
            QMessageBox.warning(self, "错误", "API未初始化")
            return

        # 验证数据
        depot_code = self.depot_combo.currentData()
        company_code = self.company_combo.currentData()
        date_str = self.date_edit.text().strip()

        if not depot_code:
            QMessageBox.warning(self, "错误", "请选择仓库")
            return

        if not company_code:
            QMessageBox.warning(self, "错误", "请选择供应商")
            return

        if not date_str:
            QMessageBox.warning(self, "错误", "请输入入库日期")
            return

        if not self.selected_items:
            QMessageBox.warning(self, "错误", "请添加商品")
            return

        # 准备提交数据
        details = []
        for item in self.selected_items:
            product = item['product']

            # 准备图片路径（如果有拍摄的照片）
            photo_paths = []
            if self.captured_photos:
                # 使用相对路径，去掉完整路径前缀
                for photo_path in self.captured_photos:
                    relative_path = os.path.basename(photo_path)
                    photo_paths.append(relative_path)

            path_str = ";".join(photo_paths) + ";" if photo_paths else ""

            detail = {
                'code': product.get('code', ''),
                'quantity': str(item['quantity']),
                'unit': product.get('unit', ''),
                'price': str(item['price']),
                'path': path_str
            }
            details.append(detail)

        submit_data = {
            'datetime': date_str,
            'depot_code': depot_code,
            'company_code': company_code,
            'details': details
        }

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.submit_btn.setEnabled(False)

        # 启动提交线程
        self.submit_thread = StockInSubmitter(self.api, submit_data)
        self.submit_thread.submit_completed.connect(self.on_submit_completed)
        self.submit_thread.error_occurred.connect(self.on_submit_error)
        self.submit_thread.progress_updated.connect(self.progress_bar.setValue)
        self.submit_thread.finished.connect(self.on_submit_finished)
        self.submit_thread.start()

        self.status_label.setText("正在提交入库申请...")

    def on_submit_completed(self, result: dict):
        """提交完成"""
        stock_in_code = result.get('stock_in_code', '')
        message = f"入库申请提交成功！\n入库单号: {stock_in_code}"

        QMessageBox.information(self, "成功", message)
        self.status_label.setText(f"✅ 入库申请提交成功 - {stock_in_code}")

        # 重置表单
        self.reset_form()

    def on_submit_error(self, error_msg: str):
        """提交错误"""
        QMessageBox.critical(self, "错误", f"提交失败: {error_msg}")
        self.status_label.setText(f"❌ 提交失败: {error_msg}")

    def on_submit_finished(self):
        """提交完成（无论成功失败）"""
        self.progress_bar.setVisible(False)
        self.submit_btn.setEnabled(True)
        self.submit_thread = None

    def reset_form(self):
        """重置表单"""
        # 重置基础信息
        self.depot_combo.setCurrentIndex(0)
        self.company_combo.setCurrentIndex(0)
        self.date_edit.setText(datetime.now().strftime("%Y-%m-%d"))

        # 清空商品列表
        self.clear_items_list()

        # 重置重量
        self.current_weight = "0.0"
        self.current_weight_label.setText("0.0 kg")
        self.manual_weight_input.setValue(0.0)
        self.weight_list.clear()

        # 清空照片
        self.captured_photos.clear()
        self.photos_list.clear()

        # 重置搜索
        self.product_search.clear()

        self.status_label.setText("表单已重置")

    def closeEvent(self, event):
        """关闭事件"""
        # 停止串口连接
        if self.serial_enabled:
            self.stop_serial_connection()

        # 停止摄像头
        if self.camera_module:
            self.camera_module.stop_camera()

        # 停止所有线程
        for thread in self.loader_threads.values():
            if thread.isRunning():
                thread.quit()
                thread.wait()

        if self.submit_thread and self.submit_thread.isRunning():
            self.submit_thread.quit()
            self.submit_thread.wait()

        super().closeEvent(event)
