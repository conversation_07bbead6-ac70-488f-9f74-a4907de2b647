#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧食堂管理系统启动脚本
Smart Canteen Management System Launcher
"""

import sys
import os
import traceback

def check_dependencies():
    """检查依赖包"""
    required_packages = ['PyQt6', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🍽️  智慧食堂管理系统")
    print("   Smart Canteen Management System")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        return
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查依赖包
    print("🔍 检查依赖包...")
    if not check_dependencies():
        return
    
    print("✅ 依赖包检查通过")
    
    # 添加项目根目录到Python路径
    project_root = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, project_root)
    
    try:
        # 导入并启动应用
        print("🚀 启动应用程序...")
        from main import main as app_main
        app_main()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请检查项目文件是否完整")
        traceback.print_exc()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
