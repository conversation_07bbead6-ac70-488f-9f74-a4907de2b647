#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证API接口地址
"""

def verify_url_construction():
    """验证URL构建逻辑"""
    base_url = "https://st.pcylsoft.com:9006/st/steelyard/"
    
    # 模拟API中的URL构建逻辑
    if base_url.endswith('/st/steelyard/') or base_url.endswith('/st/steelyard'):
        url = base_url.rstrip('/') + '/'
    else:
        url = f"{base_url}/st/steelyard/"
    
    # 添加参数
    picture_url = f"{url}?op=picture"
    
    print("🔗 API接口地址验证")
    print("=" * 50)
    print(f"Base URL: {base_url}")
    print(f"构建的URL: {url}")
    print(f"图片提交接口: {picture_url}")
    
    expected = "https://st.pcylsoft.com:9006/st/steelyard/?op=picture"
    if picture_url == expected:
        print("✅ 接口地址正确！")
    else:
        print(f"❌ 接口地址错误，期望: {expected}")
    
    return picture_url == expected

if __name__ == "__main__":
    verify_url_construction()
