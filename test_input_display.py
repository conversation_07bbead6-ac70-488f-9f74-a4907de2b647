#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试输入框显示效果
Test Input Display
"""

import sys
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QFrame
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPainter, QColor, QBrush, QLinearGradient

class TestInputWindow(QMainWindow):
    """测试输入框窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("输入框显示测试")
        self.setFixedSize(500, 400)
        
        # 设置窗口属性
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        self.init_ui()
        self.apply_styles()
        
        # 居中显示
        self.center_window()
    
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建测试卡片
        self.create_test_card(main_layout)
    
    def create_test_card(self, parent_layout):
        """创建测试卡片"""
        # 创建居中容器
        center_container = QWidget()
        center_layout = QVBoxLayout(center_container)
        center_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 测试卡片
        self.test_card = QFrame()
        self.test_card.setFixedSize(400, 300)
        self.test_card.setObjectName("testCard")
        
        # 卡片布局
        card_layout = QVBoxLayout(self.test_card)
        card_layout.setSpacing(20)
        card_layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title_label = QLabel("输入框显示测试")
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 用户名输入
        username_container = QWidget()
        username_container.setMinimumHeight(100)
        username_layout = QVBoxLayout(username_container)
        username_layout.setSpacing(10)
        
        username_label = QLabel("用户名")
        username_label.setObjectName("inputLabel")
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("请输入用户名")
        self.username_input.setObjectName("usernameInput")
        self.username_input.setMinimumHeight(60)
        self.username_input.setMaximumHeight(60)
        
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        
        # 密码输入
        password_container = QWidget()
        password_container.setMinimumHeight(100)
        password_layout = QVBoxLayout(password_container)
        password_layout.setSpacing(10)
        
        password_label = QLabel("密码")
        password_label.setObjectName("inputLabel")
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setObjectName("passwordInput")
        self.password_input.setMinimumHeight(60)
        self.password_input.setMaximumHeight(60)
        
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        
        card_layout.addWidget(title_label)
        card_layout.addWidget(username_container)
        card_layout.addWidget(password_container)
        
        center_layout.addWidget(self.test_card)
        parent_layout.addWidget(center_container)
    
    def apply_styles(self):
        """应用样式"""
        # 主窗口背景
        self.setStyleSheet("""
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(30, 30, 60, 1),
                stop:0.5 rgba(60, 30, 90, 1),
                stop:1 rgba(90, 60, 120, 1));
        }
        """)
        
        # 测试卡片样式
        self.test_card.setStyleSheet("""
        QFrame#testCard {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
        }
        """)
        
        # 标题样式
        title_label = self.findChild(QLabel, "titleLabel")
        if title_label:
            title_label.setStyleSheet("""
            QLabel#titleLabel {
                color: white;
                font-size: 20px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
            """)
        
        # 输入标签样式
        for label in self.findChildren(QLabel):
            if label.objectName() == "inputLabel":
                label.setStyleSheet("""
                QLabel#inputLabel {
                    color: rgba(255, 255, 255, 0.9);
                    font-size: 14px;
                    font-weight: 500;
                    background: transparent;
                    border: none;
                }
                """)
        
        # 输入框样式
        input_style = """
        QLineEdit {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 12px 16px;
            color: white;
            font-size: 16px;
            font-weight: 500;
            min-height: 60px;
            max-height: 60px;
        }
        QLineEdit:focus {
            border: 1px solid rgba(147, 51, 234, 0.5);
            background: rgba(255, 255, 255, 0.15);
        }
        QLineEdit::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        """
        
        self.username_input.setStyleSheet(input_style)
        self.password_input.setStyleSheet(input_style)
    
    def center_window(self):
        """居中显示窗口"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def paintEvent(self, event):
        """绘制背景"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 创建渐变背景
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(30, 30, 60))
        gradient.setColorAt(0.5, QColor(60, 30, 90))
        gradient.setColorAt(1, QColor(90, 60, 120))
        
        painter.fillRect(self.rect(), QBrush(gradient))
    
    def keyPressEvent(self, event):
        """键盘事件"""
        if event.key() == Qt.Key.Key_Escape:
            self.close()
        super().keyPressEvent(event)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = TestInputWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
