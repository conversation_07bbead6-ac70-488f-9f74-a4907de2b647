#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终布局测试 - 验证订单管理界面优化
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtCore import QTimer
from ui.modules.order_module import OrderModule

class LayoutTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("订单管理布局优化测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置背景
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
            }
        """)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建订单模块
        self.order_module = OrderModule()
        layout.addWidget(self.order_module)
        
        print("🎨 订单管理界面布局优化测试")
        print("=" * 50)
        print("✅ 主要优化项目：")
        print("1. 标题区域空间压缩：16px字体，5px间距")
        print("2. 筛选区域紧凑化：8px内边距，8px间距")
        print("3. 按钮高度限制：32px最大高度")
        print("4. 主内容区域扩展：使用stretch factor占用剩余空间")
        print("5. 表格和详情标题统一：16px字体，5px间距")
        print("6. 整体边距优化：10px外边距，5px内间距")
        print("7. 进度条高度减小：6px高度")
        print("8. 状态栏紧凑化：30px最大高度，11px字体")
        print("9. 🆕 表格行高优化：45px行高，序号完整显示")
        print("10. 🆕 表格项垂直居中对齐，提升可读性")
        print("=" * 50)
        print("📏 布局空间分配：")
        print("- 标题区域：最小化空间占用")
        print("- 筛选区域：紧凑布局，固定高度")
        print("- 主内容区域：占用大部分可用空间")
        print("- 状态栏：最小化空间占用")
        print("=" * 50)
        print("🔍 测试要点：")
        print("- 订单列表和详情区域是否获得更多垂直空间")
        print("- 上方区域是否显著减少空间占用")
        print("- 日期选择器文字是否完整显示")
        print("- 整体布局是否更加紧凑高效")
        print("- 🆕 表格行序号（1,2,3...）是否完整显示")
        print("- 🆕 表格行高是否足够，内容是否垂直居中")
        
        # 设置测试数据
        self.setup_test_data()
        
        # 5秒后自动关闭
        QTimer.singleShot(5000, self.close)
        
    def setup_test_data(self):
        """设置测试数据"""
        test_orders = [
            {
                'trade_no': 'ORDER-2023-001',
                'circulate_name': '新鲜蔬菜供应商',
                'collect_time': '2023-12-01 09:30:00',
                'state': 0,
                'item_count': 5,
                'buy_quantity': '25.5',
                'deliver_quantity': '24.8',
                'goods': [
                    {'name': '有机白菜', 'code': 'VEG001', 'buy_quantity': '10', 'deliver_quantity': '9.8', 'buy_unit': '公斤'},
                    {'name': '新鲜萝卜', 'code': 'VEG002', 'buy_quantity': '8', 'deliver_quantity': '8', 'buy_unit': '公斤'},
                    {'name': '青椒', 'code': 'VEG003', 'buy_quantity': '5', 'deliver_quantity': '5', 'buy_unit': '公斤'},
                    {'name': '西红柿', 'code': 'VEG004', 'buy_quantity': '2.5', 'deliver_quantity': '2', 'buy_unit': '公斤'}
                ]
            },
            {
                'trade_no': 'ORDER-2023-002',
                'circulate_name': '优质肉类供应商',
                'collect_time': '2023-12-01 14:15:00',
                'state': 1,
                'item_count': 3,
                'buy_quantity': '18',
                'deliver_quantity': '18',
                'goods': [
                    {'name': '猪肉', 'code': 'MEAT001', 'buy_quantity': '10', 'deliver_quantity': '10', 'buy_unit': '公斤'},
                    {'name': '牛肉', 'code': 'MEAT002', 'buy_quantity': '5', 'deliver_quantity': '5', 'buy_unit': '公斤'},
                    {'name': '鸡肉', 'code': 'MEAT003', 'buy_quantity': '3', 'deliver_quantity': '3', 'buy_unit': '公斤'}
                ]
            },
            {
                'trade_no': 'ORDER-2023-003',
                'circulate_name': '海鲜水产供应商',
                'collect_time': '2023-12-01 16:45:00',
                'state': 2,
                'item_count': 4,
                'buy_quantity': '12.5',
                'deliver_quantity': '12.5',
                'goods': []
            },
            {
                'trade_no': 'ORDER-2023-004',
                'circulate_name': '调料香料供应商',
                'collect_time': '2023-12-02 08:20:00',
                'state': 0,
                'item_count': 6,
                'buy_quantity': '8.5',
                'deliver_quantity': '8.0',
                'goods': []
            },
            {
                'trade_no': 'ORDER-2023-005',
                'circulate_name': '粮油米面供应商',
                'collect_time': '2023-12-02 10:30:00',
                'state': 1,
                'item_count': 3,
                'buy_quantity': '50',
                'deliver_quantity': '50',
                'goods': []
            },
            {
                'trade_no': 'ORDER-2023-006',
                'circulate_name': '饮料酒水供应商',
                'collect_time': '2023-12-02 14:15:00',
                'state': 3,
                'item_count': 2,
                'buy_quantity': '30',
                'deliver_quantity': '0',
                'goods': []
            },
            {
                'trade_no': 'ORDER-2023-007',
                'circulate_name': '冷冻食品供应商',
                'collect_time': '2023-12-02 16:30:00',
                'state': 2,
                'item_count': 5,
                'buy_quantity': '22.5',
                'deliver_quantity': '22.5',
                'goods': []
            },
            {
                'trade_no': 'ORDER-2023-008',
                'circulate_name': '干货坚果供应商',
                'collect_time': '2023-12-03 09:00:00',
                'state': 0,
                'item_count': 4,
                'buy_quantity': '15.8',
                'deliver_quantity': '15.0',
                'goods': []
            },
            {
                'trade_no': 'ORDER-2023-009',
                'circulate_name': '水果蔬菜供应商',
                'collect_time': '2023-12-03 11:45:00',
                'state': 1,
                'item_count': 7,
                'buy_quantity': '35.2',
                'deliver_quantity': '34.8',
                'goods': []
            },
            {
                'trade_no': 'ORDER-2023-010',
                'circulate_name': '奶制品供应商',
                'collect_time': '2023-12-03 15:20:00',
                'state': 2,
                'item_count': 3,
                'buy_quantity': '28.0',
                'deliver_quantity': '28.0',
                'goods': []
            }
        ]
        
        # 设置测试数据
        self.order_module.current_orders = test_orders
        self.order_module.populate_order_table(test_orders)
        print(f"📊 已加载 {len(test_orders)} 条测试订单数据")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = LayoutTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
