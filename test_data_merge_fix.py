#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据合并修复
Test Data Merge Fix
"""

def test_data_merge_logic():
    """测试数据合并逻辑"""
    print("🧪 测试智能数据合并逻辑")
    print("=" * 50)
    
    # 模拟基本订单数据（从订单列表获取，有完整数据）
    basic_order = {
        'trade_no': 'CD22030712669',
        'item_count': 2,
        'receive_item_count': 0,
        'buy_quantity': '2',
        'deliver_quantity': '4.11',
        'collect_time': '2022-03-07 23:10:20',
        'state': 0,
        'circulate_name': '测试供应商(勿用)',
        'count': 2,
        'goods': [
            {
                'id': 194933,
                'name': '菠萝',
                'buy_quantity': '1',
                'deliver_quantity': '3.11'
            },
            {
                'id': 194934,
                'name': '草莓',
                'buy_quantity': '1',
                'deliver_quantity': '1'
            }
        ]
    }
    
    # 模拟API详细数据（返回空数据）
    detail_data = {
        'trade_no': 'CD22030712669',
        'item_count': 0,
        'receive_item_count': 0,
        'buy_quantity': '0',
        'deliver_quantity': '0',
        'collect_time': '2022-03-07 23:10:20',
        'state': 1,
        'circulate_name': '测试供应商(勿用)',
        'count': 0,
        'goods': []
    }
    
    print("📋 基本订单数据:")
    for key, value in basic_order.items():
        if key == 'goods':
            print(f"  - {key}: {len(value)}个商品")
        else:
            print(f"  - {key}: {value}")
    
    print("\n📊 API详细数据:")
    for key, value in detail_data.items():
        if key == 'goods':
            print(f"  - {key}: {len(value)}个商品")
        else:
            print(f"  - {key}: {value}")
    
    # 旧的合并逻辑（有问题）
    print("\n❌ 旧的合并逻辑结果:")
    old_merged = {**basic_order, **detail_data}
    key_fields = ['item_count', 'buy_quantity', 'deliver_quantity', 'count', 'goods']
    for field in key_fields:
        value = old_merged.get(field, 'MISSING')
        if field == 'goods':
            print(f"  - {field}: {len(value) if isinstance(value, list) else value}")
        else:
            print(f"  - {field}: {value}")
    
    # 新的智能合并逻辑
    print("\n✅ 新的智能合并逻辑结果:")
    merged_order = basic_order.copy()
    
    for key, value in detail_data.items():
        if key == 'goods':
            # 商品数据：如果详细数据有商品，使用详细数据；否则保留基本数据
            if value and len(value) > 0:
                merged_order[key] = value
                print(f"🔄 使用详细商品数据: {len(value)}个商品")
            else:
                print(f"🔄 保留基本商品数据: {len(merged_order.get('goods', []))}个商品")
        elif key in ['item_count', 'receive_item_count', 'buy_quantity', 'deliver_quantity', 'count']:
            # 数值字段：如果详细数据不为0或空，才使用详细数据
            if value and str(value) != '0' and value != 0:
                merged_order[key] = value
                print(f"🔄 使用详细数据 {key}: {value}")
            else:
                print(f"🔄 保留基本数据 {key}: {merged_order.get(key, 'N/A')}")
        else:
            # 其他字段：直接使用详细数据（如果存在）
            if value is not None and value != '':
                merged_order[key] = value
    
    print("\n🔗 最终合并结果:")
    for field in key_fields:
        value = merged_order.get(field, 'MISSING')
        if field == 'goods':
            print(f"  - {field}: {len(value) if isinstance(value, list) else value}个商品")
            if isinstance(value, list) and len(value) > 0:
                for i, good in enumerate(value[:2]):
                    print(f"    商品{i+1}: {good.get('name', 'N/A')} - 采购:{good.get('buy_quantity', 'N/A')}")
        else:
            print(f"  - {field}: {value}")
    
    print("\n" + "=" * 50)
    print("🎯 修复总结:")
    print("✅ 保留了基本订单的有效数据")
    print("✅ 避免了空的详细数据覆盖有效数据")
    print("✅ 商品信息正确显示")
    print("✅ 数值字段显示正确")

def test_flag_parameter():
    """测试flag参数逻辑"""
    print("\n🔧 测试flag参数逻辑")
    print("=" * 30)
    
    # 模拟不同flag值的API响应
    responses = {
        0: {  # 未验收
            'code': 200,
            'data': {
                'count': 2,
                'goods': [{'name': '菠萝'}, {'name': '草莓'}]
            }
        },
        1: {  # 已验收
            'code': 200,
            'data': {
                'count': 0,
                'goods': []
            }
        }
    }
    
    print("📊 模拟API响应:")
    for flag, response in responses.items():
        data = response.get('data', {})
        count = data.get('count', 0)
        goods_count = len(data.get('goods', []))
        status = "未验收" if flag == 0 else "已验收"
        print(f"  - flag={flag} ({status}): count={count}, goods={goods_count}个")
    
    # 模拟新的逻辑
    print("\n🔄 智能flag选择逻辑:")
    
    # 先尝试flag=0
    flag0_response = responses[0]
    print(f"1. 尝试flag=0: count={flag0_response['data']['count']}")
    
    if (flag0_response.get('code') == 200 and 
        flag0_response.get('data', {}).get('count', 0) == 0 and 
        len(flag0_response.get('data', {}).get('goods', [])) == 0):
        
        print("2. flag=0数据为空，尝试flag=1")
        flag1_response = responses[1]
        print(f"3. flag=1结果: count={flag1_response['data']['count']}")
        final_response = flag1_response
    else:
        print("2. flag=0有数据，使用flag=0结果")
        final_response = flag0_response
    
    print(f"✅ 最终选择: count={final_response['data']['count']}, goods={len(final_response['data']['goods'])}个")

if __name__ == '__main__':
    test_data_merge_logic()
    test_flag_parameter()
    
    print("\n" + "=" * 60)
    print("🎉 修复完成！现在订单详情应该能正确显示数据了")
    print("💡 关键改进:")
    print("1. 智能数据合并 - 保留有效数据，忽略空数据")
    print("2. 双flag尝试 - 先尝试未验收，再尝试已验收")
    print("3. 详细日志 - 完整的数据流程监控")
    print("=" * 60)
