# 历史账号功能说明

## 功能设计说明

### 🔒 安全设计原则

历史账号功能**只保存用户名，不保存密码**，这是出于安全考虑的正确设计：

#### ✅ 为什么不保存密码？
1. **安全风险** - 保存密码会带来严重的安全隐患
2. **行业标准** - 所有正规应用都不会明文保存用户密码
3. **法规要求** - 数据保护法规禁止明文存储敏感信息
4. **最佳实践** - 即使加密存储也存在被破解的风险

#### ✅ 正确的使用流程
1. **首次登录** - 输入用户名和密码，勾选"记住账号"
2. **保存历史** - 系统只保存用户名和登录时间
3. **再次使用** - 点击"历史账号"选择用户名
4. **输入密码** - 用户需要重新输入密码完成登录

## 功能特性

### 📋 历史账号包含的信息
- ✅ **用户名** - 用于快速选择
- ✅ **登录次数** - 显示使用频率
- ✅ **最后登录时间** - 显示最近使用时间
- ❌ **密码** - 出于安全考虑不保存

### 🎯 使用场景
1. **多账号切换** - 在不同账号间快速切换
2. **减少输入** - 避免重复输入用户名
3. **提高效率** - 快速选择常用账号
4. **历史记录** - 查看登录历史

## 使用方法

### 1. 保存历史账号
```
1. 输入用户名和密码
2. 勾选"记住账号"选项
3. 点击"登录"按钮
4. 登录成功后自动保存到历史
```

### 2. 使用历史账号
```
1. 点击"历史账号"按钮
2. 在弹出的对话框中选择账号
3. 双击账号或点击"选择"按钮
4. 用户名自动填充，光标跳转到密码框
5. 输入密码完成登录
```

### 3. 管理历史账号
```
1. 点击"历史账号"按钮
2. 选择要删除的账号
3. 点击"删除"按钮
4. 确认删除操作
```

## 技术实现

### 存储格式
```json
{
  "login_history": [
    {
      "username": "用户名",
      "last_login": 1640995200,  // 时间戳
      "login_count": 5           // 登录次数
    }
  ]
}
```

### 安全措施
1. **不存储密码** - 任何形式都不保存密码信息
2. **本地存储** - 历史记录只保存在本地设备
3. **用户控制** - 用户可以随时删除历史记录
4. **自动清理** - 最多保存10个历史账号

## 常见问题

### Q: 为什么历史账号没有密码？
A: 这是正确的安全设计。保存密码会带来严重的安全风险，所有正规应用都不会保存用户密码。

### Q: 如何实现自动登录？
A: 系统使用Token机制实现自动登录：
- 登录成功后保存访问Token
- 下次启动时验证Token有效性
- Token有效则直接进入主界面
- Token过期则显示登录界面

### Q: 历史账号有什么用？
A: 历史账号的作用是：
- 快速选择用户名，避免重复输入
- 在多个账号间快速切换
- 查看登录历史和使用频率
- 提高登录效率

### Q: 如何清除历史记录？
A: 可以通过以下方式清除：
- 在历史账号对话框中选择并删除单个账号
- 删除 `config/auth_storage.json` 文件清除所有记录

### Q: 历史账号安全吗？
A: 是的，历史账号功能是安全的：
- 只保存用户名，不保存密码
- 数据只存储在本地设备
- 用户可以随时删除记录
- 符合安全最佳实践

## 对比其他应用

### 浏览器
- Chrome/Firefox：保存用户名，密码需要主密码解锁
- 我们的应用：只保存用户名，密码每次重新输入

### 移动应用
- 微信/QQ：使用Token自动登录，不保存密码
- 银行应用：只保存用户名，密码每次输入

### 桌面应用
- VS Code：保存用户名，使用Token认证
- 我们的应用：保存用户名+Token，密码不保存

## 总结

历史账号功能的设计是**正确且安全**的：

✅ **符合安全标准** - 不保存敏感的密码信息
✅ **提高用户体验** - 快速选择用户名
✅ **保护用户隐私** - 数据只存储在本地
✅ **易于管理** - 用户可以随时删除记录

这种设计在安全性和便利性之间取得了最佳平衡，是行业标准的实现方式。
