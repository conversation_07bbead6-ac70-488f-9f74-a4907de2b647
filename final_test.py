#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试脚本
Final Test Script
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """测试基础导入"""
    print("测试基础导入...")
    
    try:
        import PyQt6
        print(f"✅ PyQt6 版本: {PyQt6.QtCore.PYQT_VERSION_STR}")
    except ImportError:
        print("❌ PyQt6 未安装")
        return False
    
    try:
        import requests
        print(f"✅ requests 版本: {requests.__version__}")
    except ImportError:
        print("❌ requests 未安装")
        return False
    
    return True

def test_qt_application():
    """测试Qt应用创建"""
    print("\n测试Qt应用创建...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        
        # 创建应用（如果还没有的话）
        if not QApplication.instance():
            app = QApplication(sys.argv)
        else:
            app = QApplication.instance()
        
        print("✅ QApplication 创建成功")
        return app
    except Exception as e:
        print(f"❌ QApplication 创建失败: {e}")
        return None

def test_window_creation():
    """测试窗口创建"""
    print("\n测试窗口创建...")
    
    try:
        from PyQt6.QtWidgets import QMainWindow, QLabel
        from PyQt6.QtCore import Qt
        
        # 创建测试窗口
        window = QMainWindow()
        window.setWindowTitle("测试窗口")
        window.setFixedSize(400, 300)
        
        label = QLabel("测试成功！窗口可以正常创建")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        window.setCentralWidget(label)
        
        print("✅ 测试窗口创建成功")
        return window
    except Exception as e:
        print(f"❌ 窗口创建失败: {e}")
        return None

def test_project_modules():
    """测试项目模块"""
    print("\n测试项目模块...")
    
    modules_status = {}
    
    # 测试样式模块
    try:
        from ui.styles import GlassmorphismStyles
        modules_status['styles'] = True
        print("✅ 样式模块导入成功")
    except Exception as e:
        modules_status['styles'] = False
        print(f"❌ 样式模块导入失败: {e}")
    
    # 测试配置模块
    try:
        from config.settings import settings
        modules_status['settings'] = True
        print("✅ 配置模块导入成功")
    except Exception as e:
        modules_status['settings'] = False
        print(f"❌ 配置模块导入失败: {e}")
    
    # 测试API模块
    try:
        from api.auth_api import AuthAPI
        modules_status['api'] = True
        print("✅ API模块导入成功")
    except Exception as e:
        modules_status['api'] = False
        print(f"❌ API模块导入失败: {e}")
    
    return modules_status

def test_login_window():
    """测试登录窗口"""
    print("\n测试登录窗口...")
    
    try:
        from ui.login_window import LoginWindow
        
        # 创建登录窗口
        login_window = LoginWindow()
        print("✅ 登录窗口创建成功")
        
        return login_window
    except Exception as e:
        print(f"❌ 登录窗口创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 智慧食堂管理系统 - 最终测试")
    print("=" * 60)
    
    # 测试1: 基础导入
    if not test_basic_imports():
        print("\n❌ 基础依赖测试失败，请安装必要的包")
        print("运行: pip install PyQt6 requests")
        return False
    
    # 测试2: Qt应用创建
    app = test_qt_application()
    if not app:
        print("\n❌ Qt应用创建失败")
        return False
    
    # 测试3: 窗口创建
    test_window = test_window_creation()
    if not test_window:
        print("\n❌ 窗口创建失败")
        return False
    
    # 测试4: 项目模块
    modules_status = test_project_modules()
    
    # 测试5: 登录窗口
    login_window = test_login_window()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    print("✅ 基础依赖: 通过")
    print("✅ Qt应用: 通过")
    print("✅ 窗口创建: 通过")
    
    print(f"{'✅' if modules_status.get('styles') else '❌'} 样式模块: {'通过' if modules_status.get('styles') else '失败'}")
    print(f"{'✅' if modules_status.get('settings') else '❌'} 配置模块: {'通过' if modules_status.get('settings') else '失败'}")
    print(f"{'✅' if modules_status.get('api') else '❌'} API模块: {'通过' if modules_status.get('api') else '失败'}")
    print(f"{'✅' if login_window else '❌'} 登录窗口: {'通过' if login_window else '失败'}")
    
    if login_window:
        print("\n🎉 所有核心测试通过！")
        print("💡 可以尝试启动完整程序:")
        print("   python main.py")
        print("   或双击 start.bat")
        
        # 可选：显示登录窗口进行视觉测试
        response = input("\n是否显示登录窗口进行视觉测试？(y/n): ").lower().strip()
        if response == 'y':
            print("显示登录窗口...")
            login_window.show()
            print("关闭窗口以继续...")
            app.exec()
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
        return False
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 测试完成")
        else:
            print("\n❌ 测试失败")
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
