#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证URL修复
"""

import os
import json

def check_auth_api():
    """检查AuthAPI类的默认URL"""
    print("🔍 检查 api/auth_api.py")
    try:
        with open('api/auth_api.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'https://st.pcylsoft.com:9006/st/steelyard/' in content:
            print("  ✅ AuthAPI默认URL已修复")
            return True
        elif 'https://[ip:port]/st/steelyard/' in content:
            print("  ❌ AuthAPI仍包含占位符URL")
            return False
        else:
            print("  ⚠️  未找到预期的URL模式")
            return False
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return False

def check_settings():
    """检查Settings类的默认配置"""
    print("\n🔍 检查 config/settings.py")
    try:
        with open('config/settings.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'https://st.pcylsoft.com:9006/st/steelyard/' in content:
            print("  ✅ Settings默认URL已修复")
            return True
        elif 'https://[ip:port]/st/steelyard/' in content:
            print("  ❌ Settings仍包含占位符URL")
            return False
        else:
            print("  ⚠️  未找到预期的URL模式")
            return False
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return False

def check_main_window():
    """检查MainWindow中的API实例化"""
    print("\n🔍 检查 ui/main_window.py")
    try:
        with open('ui/main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'settings.api_base_url' in content:
            print("  ✅ MainWindow使用配置文件中的API地址")
            return True
        elif 'https://st.pcylsoft.com:9006/st/steelyard/' in content:
            print("  ⚠️  MainWindow使用硬编码API地址（可接受）")
            return True
        else:
            print("  ❌ MainWindow API地址配置有问题")
            return False
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return False

def check_config_file():
    """检查配置文件"""
    print("\n🔍 检查 config/app_config.json")
    try:
        with open('config/app_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        base_url = config.get('api', {}).get('base_url', '')
        if base_url == 'https://st.pcylsoft.com:9006/st/steelyard/':
            print("  ✅ 配置文件API地址正确")
            return True
        else:
            print(f"  ❌ 配置文件API地址错误: {base_url}")
            return False
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return False

def check_release_config():
    """检查发布包中的配置文件"""
    print("\n🔍 检查 release/config/app_config.json")
    try:
        with open('release/config/app_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        base_url = config.get('api', {}).get('base_url', '')
        if base_url == 'https://st.pcylsoft.com:9006/st/steelyard/':
            print("  ✅ 发布包配置文件API地址正确")
            return True
        else:
            print(f"  ❌ 发布包配置文件API地址错误: {base_url}")
            return False
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 智慧食堂管理系统 - URL修复验证")
    print("=" * 50)
    
    checks = [
        ("AuthAPI默认URL", check_auth_api),
        ("Settings默认URL", check_settings),
        ("MainWindow API配置", check_main_window),
        ("配置文件", check_config_file),
        ("发布包配置", check_release_config)
    ]
    
    passed = 0
    total = len(checks)
    
    for name, check_func in checks:
        if check_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 验证结果: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 所有URL修复验证通过！")
        print("\n💡 预期效果:")
        print("- 登录时不再出现占位符URL错误")
        print("- API请求使用正确的服务器地址")
        print("- 程序能够正常连接到API服务器")
        
        print("\n🚀 可以安全使用修复后的程序！")
    else:
        print("❌ 部分检查未通过，可能仍存在问题")
        print("请检查上述失败的项目")
    
    return passed == total

if __name__ == "__main__":
    main()
