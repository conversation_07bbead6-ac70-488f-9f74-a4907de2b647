#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的API
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_api_with_token():
    """使用实际token测试API"""
    try:
        from api.stock_api import StockAPI
        from config.settings import Settings
        
        # 加载设置
        settings = Settings()
        print(f"🔍 API基础URL: {settings.api_base_url}")
        
        # 创建API实例
        api = StockAPI(settings.api_base_url)
        print(f"✅ StockAPI实例创建成功")
        
        # 设置一个测试token（这里需要一个有效的token）
        # 从控制台信息中我们知道有一个有效的token: ED0DBD864A...
        test_token = "ED0DBD864A8E4E5E8E8E8E8E8E8E8E8E"  # 这只是示例，需要实际的完整token
        
        # 如果你有实际的token，请替换上面的值
        print("⚠️ 注意：需要设置实际的access_token才能测试API调用")
        print("⚠️ 请从应用程序的登录状态中获取有效的token")
        
        # 这里我们只测试API结构，不进行实际调用
        print("🔍 测试API方法是否存在...")
        
        # 检查方法是否存在
        methods = ['get_depot_list', 'get_project_list', 'get_meal_time_list', 'get_stock_list']
        for method in methods:
            if hasattr(api, method):
                print(f"✅ {method} 方法存在")
            else:
                print(f"❌ {method} 方法不存在")
        
        print("🎉 API结构测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def test_url_construction():
    """测试URL构造"""
    try:
        from api.stock_api import StockAPI
        
        # 测试基础URL
        base_url = "https://st.pcylsoft.com:9006/st/steelyard"
        api = StockAPI(base_url)
        
        print(f"🔍 基础URL: {api.base_url}")
        
        # 模拟URL构造（不实际调用）
        endpoints = [
            "?op=stock_depot",
            "?op=stock_project", 
            "?op=stock_time",
            "?op=stocks"
        ]
        
        for endpoint in endpoints:
            full_url = f"{api.base_url}/{endpoint.lstrip('/')}"
            print(f"🔗 完整URL: {full_url}")
        
        print("✅ URL构造测试完成")
        
    except Exception as e:
        print(f"❌ URL构造测试失败: {e}")

def main():
    """主函数"""
    print("🚀 开始测试修复后的API")
    print("=" * 50)
    
    # 测试URL构造
    test_url_construction()
    
    print("\n" + "=" * 50)
    
    # 测试API结构
    test_api_with_token()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成")
    print("\n📝 修复说明:")
    print("1. ✅ 修复了API参数传递方式：op参数现在通过URL传递")
    print("2. ✅ 修复了认证header：使用access_token而不是Authorization")
    print("3. ✅ 移除了调试信息以提高性能")
    print("\n🔧 修复的API调用:")
    print("- 仓库列表: GET ?op=stock_depot")
    print("- 项目列表: GET ?op=stock_project") 
    print("- 餐别列表: GET ?op=stock_time")
    print("- 库存列表: GET ?op=stocks")
    print("\n🔑 认证方式:")
    print("- Header: access_token: <your_token>")

if __name__ == "__main__":
    main()
