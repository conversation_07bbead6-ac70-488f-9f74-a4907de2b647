#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口测试脚本
API Interface Test Script
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.auth_api import AuthAPI
from config.settings import settings

def test_login():
    """测试登录接口"""
    print("=== 智慧食堂管理系统 API 测试 ===")
    print(f"API地址: {settings.api_base_url}")
    print(f"API版本: {settings.api_version}")
    print()
    
    # 创建API实例
    auth_api = AuthAPI(settings.api_base_url)
    
    # 测试用户名和密码（请根据实际情况修改）
    test_username = input("请输入测试用户名: ").strip()
    test_password = input("请输入测试密码: ").strip()
    
    if not test_username or not test_password:
        print("用户名和密码不能为空！")
        return
    
    print("\n正在测试登录接口...")
    
    try:
        # 调用登录接口
        result = auth_api.login(test_username, test_password, settings.api_version)
        
        print(f"响应结果: {result}")
        
        if result.get('code') == 200:
            print("✅ 登录成功！")
            data = result.get('data', {})
            access_token = data.get('access_token')
            picture_upload_url = data.get('picture_upload_url')
            
            if access_token:
                print(f"Access Token: {access_token}")
                print(f"图片上传地址: {picture_upload_url}")
                
                # 测试认证状态
                print(f"认证状态: {'已认证' if auth_api.is_authenticated() else '未认证'}")
            else:
                print("⚠️ 警告: 响应中没有access_token")
        else:
            print(f"❌ 登录失败: {result.get('msg', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")

def test_config():
    """测试配置文件"""
    print("=== 配置文件测试 ===")
    print(f"应用名称: {settings.app_name}")
    print(f"应用版本: {settings.app_version}")
    print(f"组织名称: {settings.organization}")
    print(f"API基础URL: {settings.api_base_url}")
    print(f"API超时时间: {settings.api_timeout}秒")
    print(f"API版本: {settings.api_version}")
    print(f"窗口宽度: {settings.window_width}")
    print(f"窗口高度: {settings.window_height}")
    print(f"全屏模式: {'是' if settings.fullscreen else '否'}")
    print(f"主题: {settings.theme}")
    print(f"记住用户名: {'是' if settings.remember_username else '否'}")
    print(f"自动登录: {'是' if settings.auto_login else '否'}")
    print()

if __name__ == "__main__":
    test_config()
    test_login()
