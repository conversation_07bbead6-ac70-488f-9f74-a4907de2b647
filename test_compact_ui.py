#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
压缩界面测试脚本
Compact UI Test Script
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

# 导入重量提交模块
from ui.modules.weight_submission_module import WeightSubmissionModule

class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("压缩界面测试 - 重量提交模块")
        self.setGeometry(100, 100, 800, 600)
        
        # 设置深色背景
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a1a2e, stop:0.5 #16213e, stop:1 #0f3460);
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 添加重量提交模块
        self.weight_module = WeightSubmissionModule()
        
        # 设置测试数据
        self.setup_test_data()
        
        layout.addWidget(self.weight_module)
    
    def setup_test_data(self):
        """设置测试数据"""
        # 模拟商品信息
        test_product = {
            'name': '久大食用盐 500g/袋',
            'code': 'JD001',
            'order_id': 'ORD20241201001',
            'stock_mode': '入库'
        }
        
        self.weight_module.set_product_info(
            test_product['name'],
            test_product['code'], 
            test_product['order_id'],
            test_product['stock_mode']
        )
        
        # 模拟重量数据
        self.weight_module.current_weight_label.setText("2.35 kg")
        
        # 模拟串口连接状态
        self.weight_module.serial_status_label.setText("🟢 串口已连接 (COM4)")
        
        # 添加一些测试重量
        test_weights = [2.30, 2.35, 2.32, 2.34]
        for weight in test_weights:
            self.weight_module.current_weights.append(weight)
        
        self.weight_module.update_weight_list_display()
        
        # 更新状态
        self.weight_module.status_label.setText(f"✅ 已添加 {len(test_weights)} 个重量记录")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序字体
    font = QFont("Inter", 10)
    app.setFont(font)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    print("🎯 压缩界面测试")
    print("=" * 50)
    print("✅ 界面优化内容:")
    print("1. 减少边距和间距")
    print("2. 压缩字体大小")
    print("3. 合并重量显示和列表区域")
    print("4. 使用水平布局节约垂直空间")
    print("5. 预留摄像头区域")
    print("6. 保持文字完整显示")
    print()
    print("📏 空间节约:")
    print("- 标题高度: 20px -> 14px")
    print("- 商品信息: 紧凑网格布局")
    print("- 重量显示: 32px -> 20px 字体")
    print("- 按钮高度: 45px -> 28px")
    print("- 重量列表: 150px -> 80px 高度")
    print()
    print("🔧 测试功能:")
    print("- 检查所有文字是否完整显示")
    print("- 验证按钮是否可正常点击")
    print("- 确认布局是否紧凑合理")
    print("- 摄像头区域是否预留充足")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
