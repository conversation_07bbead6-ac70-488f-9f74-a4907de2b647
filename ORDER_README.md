# 📦 订单管理功能说明

## 功能概述

订单管理模块为智慧食堂管理系统提供了完整的订单查询、搜索和管理功能。支持按日期筛选、供应商搜索、订单详情查看等操作。

## 🎯 主要功能

### 1. 订单查询
- **获取所有订单**: 查看系统中的所有订单
- **按日期查询**: 查看指定日期的订单
- **今日订单**: 快速查看当天的订单情况

### 2. 订单搜索
- **按订单号搜索**: 精确查找特定订单
- **按供应商搜索**: 查看特定供应商的所有订单
- **实时筛选**: 支持多条件组合筛选

### 3. 订单详情
- **基本信息**: 订单号、供应商、时间、状态等
- **商品详情**: 商品名称、编码、数量、单位等
- **状态跟踪**: 订单处理进度和收货情况

### 4. 数据统计
- **订单统计**: 总数、商品种类、数量统计
- **供应商分析**: 供应商分布和订单量
- **状态分布**: 各状态订单的数量分布

## 🔧 API接口说明

### 接口地址
```
POST https://st.pcylsoft.com:9006/st/steelyard/?op=order
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| day | String | 否 | 日期，格式：YYYY-MM-DD |

### 请求头
```
Authorization: your_access_token
Content-Type: application/x-www-form-urlencoded
```

### 响应格式
```json
{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "trade_no": "CD22030712669",
            "item_count": 2,
            "receive_item_count": 0,
            "buy_quantity": "2",
            "deliver_quantity": "4.11",
            "collect_time": "2022-03-07 23:10:20",
            "state": 0,
            "circulate_name": "测试供应商(勿用)",
            "count": 2,
            "goods": [
                {
                    "id": 194933,
                    "spec": null,
                    "code": "13010501",
                    "name": "菠萝",
                    "buy_unit": "市斤",
                    "buy_quantity": "1",
                    "stock_quantity": 3,
                    "deliver_unit": "市斤",
                    "deliver_quantity": "3.11",
                    "receive_quantity": null,
                    "stock_mode": 1,
                    "receive_date": null,
                    "path": null,
                    "batch": null
                }
            ]
        }
    ],
    "total": 6
}
```

## 📋 状态说明

| 状态码 | 状态名称 | 颜色标识 | 说明 |
|--------|----------|----------|------|
| 0 | 待处理 | 黄色 | 订单已创建，等待处理 |
| 1 | 处理中 | 蓝色 | 订单正在处理中 |
| 2 | 已完成 | 绿色 | 订单已完成处理 |
| 3 | 已取消 | 红色 | 订单已取消 |

## 💻 代码使用示例

### 1. 基本API调用
```python
from api.order_api import OrderAPI

# 创建API实例
api = OrderAPI("https://st.pcylsoft.com:9006/st/steelyard/")
api.set_access_token(your_access_token)

# 获取所有订单
result = api.get_orders()

# 获取指定日期订单
result = api.get_orders("2023-06-08")

# 获取今日订单
result = api.get_today_orders()
```

### 2. 订单搜索
```python
# 按订单号搜索
order = api.search_orders_by_trade_no("CD22030712669")

# 按供应商搜索
orders = api.search_orders_by_supplier("测试供应商")
```

### 3. 统计分析
```python
# 获取统计信息
stats = api.get_order_statistics()
print(f"总订单数: {stats['total_orders']}")
print(f"供应商数量: {len(stats['suppliers'])}")
```

### 4. UI模块集成
```python
from ui.modules.order_module import OrderModule

# 创建订单模块
order_module = OrderModule()
order_module.set_api(api)

# 添加到主界面
main_widget.addWidget(order_module)
```

## 🎨 用户界面说明

### 筛选区域
- **日期选择器**: 选择特定日期的订单
- **供应商搜索框**: 按供应商名称筛选
- **订单号搜索框**: 按订单号精确搜索
- **快捷按钮**: 今日订单、所有订单、刷新

### 订单列表
- **表格显示**: 订单基本信息一览
- **状态标识**: 不同颜色表示订单状态
- **排序功能**: 支持按列排序
- **选择操作**: 点击查看详情

### 订单详情
- **基本信息**: 订单的基础数据
- **商品列表**: 详细的商品信息
- **格式化显示**: 友好的数据展示
- **滚动查看**: 支持长内容滚动

## 🚀 快速开始

### 1. 启动系统
```bash
python run.py
```

### 2. 登录系统
- 输入用户名和密码
- 获取访问令牌

### 3. 进入订单管理
- 点击侧边栏"订单管理"
- 开始使用各项功能

### 4. 基本操作
1. 点击"今日订单"查看当天订单
2. 使用搜索框筛选特定订单
3. 点击订单行查看详细信息
4. 使用日期选择器查看历史订单

## 🔍 测试和验证

### 运行测试脚本
```bash
python test_order_api.py
```

### 运行演示脚本
```bash
python demo_order.py
```

### 验证功能
```bash
python verify_order.py
```

## ⚠️ 注意事项

1. **网络连接**: 确保有稳定的网络连接
2. **访问令牌**: 需要有效的API访问令牌
3. **权限验证**: 确保账户有订单查看权限
4. **错误处理**: 注意处理网络异常和API错误
5. **数据缓存**: 考虑实现适当的数据缓存策略

## 📁 文件结构

```
├── api/
│   ├── order_api.py          # 订单API接口
│   └── auth_api.py           # 认证API基类
├── ui/
│   └── modules/
│       └── order_module.py   # 订单UI模块
├── test_order_api.py         # API测试脚本
├── demo_order.py             # 功能演示脚本
├── verify_order.py           # 功能验证脚本
└── ORDER_README.md           # 本说明文件
```

## 🎉 功能特点

- ✅ **功能完整**: 支持多种查询和搜索方式
- ✅ **界面友好**: 直观的操作界面和状态提示
- ✅ **响应迅速**: 异步加载和实时筛选
- ✅ **易于集成**: 模块化设计，便于扩展
- ✅ **错误处理**: 完善的异常处理机制
- ✅ **文档完整**: 详细的使用说明和示例

## 📞 技术支持

如有问题或建议，请联系开发团队或查看相关文档。
