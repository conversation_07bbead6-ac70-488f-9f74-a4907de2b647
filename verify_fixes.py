#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复结果
Verify Fixes
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_picture_api_fix():
    """验证图片API修复"""
    print("🔧 验证图片API修复")
    print("=" * 40)
    
    try:
        # 读取API文件内容
        with open('api/weight_api.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有错误的方法调用
        if 'get_headers()' in content:
            print("❌ 仍然包含错误的get_headers()调用")
            return False
        
        if "'Authorization': self.access_token" in content:
            print("✅ 正确设置Authorization头")
        else:
            print("❌ 未找到正确的Authorization头设置")
            return False
        
        if 'headers=headers' in content:
            print("✅ 正确传递headers参数")
        else:
            print("❌ 未找到正确的headers参数传递")
            return False
        
        print("✅ 图片API修复验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_serial_output_suppression():
    """验证串口输出抑制"""
    print("\n📡 验证串口输出抑制")
    print("=" * 40)
    
    try:
        # 读取重量提交模块文件内容
        with open('ui/modules/weight_submission_module.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查调试输出是否被注释
        debug_patterns = [
            '# print(f"📡 串口数据解析成功',
            '# print(f"⏳ 重量数据不稳定',
            '# print(f"❌ 重量数据格式错误',
            '# print(f"❌ 串口数据格式不匹配'
        ]
        
        suppressed_count = 0
        for pattern in debug_patterns:
            if pattern in content:
                suppressed_count += 1
        
        if suppressed_count >= 3:  # 至少3个调试输出被抑制
            print(f"✅ 串口调试输出已抑制 ({suppressed_count}/4)")
        else:
            print(f"⚠️  部分串口调试输出未抑制 ({suppressed_count}/4)")
        
        return suppressed_count >= 3
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_product_id_usage():
    """验证商品ID使用"""
    print("\n🆔 验证商品ID使用")
    print("=" * 40)
    
    try:
        # 读取重量提交模块文件内容
        with open('ui/modules/weight_submission_module.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复点
        checks = [
            ("商品ID获取", "self.current_product.get('id')" in content),
            ("显示标签修正", "商品ID: {product_id}" in content),
            ("API提交参数", "id=order_detail_id" in content)
        ]
        
        passed = 0
        for check_name, check_result in checks:
            if check_result:
                print(f"  ✅ {check_name}")
                passed += 1
            else:
                print(f"  ❌ {check_name}")
        
        return passed == len(checks)
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🔍 修复结果验证")
    print("=" * 60)
    
    # 验证各项修复
    picture_fix = verify_picture_api_fix()
    serial_fix = verify_serial_output_suppression()
    product_id_fix = verify_product_id_usage()
    
    print("\n📊 验证总结")
    print("=" * 60)
    
    if picture_fix and serial_fix and product_id_fix:
        print("✅ 所有修复验证通过")
        
        print("\n🎯 修复内容总结:")
        print("1. ✅ 图片提交API错误修复")
        print("   - 移除了错误的get_headers()调用")
        print("   - 正确设置Authorization头")
        print("   - 使用正确的headers参数")
        
        print("\n2. ✅ 串口调试输出抑制")
        print("   - 注释了串口数据解析的调试输出")
        print("   - 控制台不再显示串口数据")
        print("   - 保持功能正常运行")
        
        print("\n3. ✅ 商品ID使用修正")
        print("   - 使用商品的'id'字段(194933)而不是'code'字段")
        print("   - 修正了界面显示标签")
        print("   - API提交使用正确的商品ID")
        
        print("\n💡 预期效果:")
        print("- 重量提交后图片提交应该成功")
        print("- 控制台不再输出串口调试信息")
        print("- 使用正确的商品ID进行API操作")
        
    else:
        print("❌ 部分修复验证失败")
        if not picture_fix:
            print("  - 图片API修复验证失败")
        if not serial_fix:
            print("  - 串口输出抑制验证失败")
        if not product_id_fix:
            print("  - 商品ID使用验证失败")

if __name__ == "__main__":
    main()
