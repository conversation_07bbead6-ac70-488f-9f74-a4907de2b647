#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速样式测试
Quick Style Test
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    print("🎨 输入框样式修复验证")
    print("=" * 50)
    
    try:
        # 测试样式导入
        from ui.styles import GlassmorphismStyles
        print("✅ 样式模块导入成功")
        
        # 获取输入框样式
        input_style = GlassmorphismStyles.get_input_style()
        print("✅ 输入框样式获取成功")
        
        # 检查关键样式属性
        checks = [
            ("border-radius: 16px", "圆角半径"),
            ("padding: 16px 20px", "内边距"),
            ("min-height: 24px", "最小高度"),
            ("max-height: 48px", "最大高度"),
            ("box-shadow", "阴影效果")
        ]
        
        print("\n🔍 样式检查:")
        for check, desc in checks:
            if check in input_style:
                print(f"  ✅ {desc}: {check}")
            else:
                print(f"  ❌ {desc}: 未找到 {check}")
        
        # 测试登录窗口导入
        from ui.login_window import LoginWindow
        print("✅ 登录窗口导入成功")
        
        print("\n📋 修复总结:")
        print("  • 输入框圆角半径统一为16px")
        print("  • 输入框高度设置为56px")
        print("  • 内边距增加到16px 20px")
        print("  • 添加焦点时的阴影效果")
        print("  • 确保文字完整显示")
        
        print("\n🎉 样式修复验证通过！")
        print("现在可以运行程序查看效果:")
        print("  python main.py")
        print("  python start_fixed.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
