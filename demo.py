#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧食堂管理系统演示脚本
Smart Canteen Management System Demo
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.canteen_api import CanteenAPI
from config.settings import settings

def demo_api_usage():
    """演示API使用"""
    print("=== 智慧食堂管理系统 API 演示 ===\n")
    
    # 创建API实例
    api = CanteenAPI(settings.api_base_url)
    
    print("1. 测试登录接口")
    print("-" * 30)
    
    # 模拟登录（实际使用时需要真实的用户名和密码）
    username = "demo_user"
    password = "demo_password"
    
    print(f"尝试登录用户: {username}")
    login_result = api.login(username, password)
    print(f"登录结果: {login_result}")
    
    if login_result.get('code') == 200:
        print("✅ 登录成功！")
        
        print("\n2. 测试其他API接口")
        print("-" * 30)
        
        # 测试仓库列表
        print("获取仓库列表...")
        warehouse_result = api.get_warehouse_list()
        print(f"仓库列表: {warehouse_result}")
        
        # 测试项目列表
        print("\n获取项目列表...")
        project_result = api.get_project_list()
        print(f"项目列表: {project_result}")
        
        # 测试餐别列表
        print("\n获取餐别列表...")
        meal_result = api.get_meal_list()
        print(f"餐别列表: {meal_result}")
        
        # 测试库存列表
        print("\n获取库存列表...")
        inventory_result = api.get_inventory_list()
        print(f"库存列表: {inventory_result}")
        
        # 测试食谱列表
        print("\n获取食谱列表...")
        recipe_result = api.get_recipe_list()
        print(f"食谱列表: {recipe_result}")
        
        # 测试采购订单
        print("\n获取采购订单...")
        order_result = api.get_purchase_orders()
        print(f"采购订单: {order_result}")
        
        # 测试打印记录
        print("\n获取打印记录...")
        print_result = api.get_print_records()
        print(f"打印记录: {print_result}")
        
    else:
        print("❌ 登录失败，无法测试其他接口")
        print(f"错误信息: {login_result.get('msg', '未知错误')}")

def demo_ui_components():
    """演示UI组件"""
    print("\n=== UI 组件演示 ===\n")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.login_window import LoginWindow
        from ui.main_window import MainWindow
        
        print("✅ UI组件导入成功")
        print("- LoginWindow: 登录窗口")
        print("- MainWindow: 主界面窗口")
        print("- GlassmorphismStyles: 玻璃态样式系统")
        
        print("\n🎨 设计特色:")
        print("- 采用现代化的玻璃态(Glassmorphism)设计风格")
        print("- 半透明背景和模糊效果")
        print("- 渐变色彩和柔和阴影")
        print("- 响应式交互动画")
        
    except ImportError as e:
        print(f"❌ UI组件导入失败: {e}")
        print("请确保已安装PyQt6: pip install PyQt6")

def demo_config_system():
    """演示配置系统"""
    print("\n=== 配置系统演示 ===\n")
    
    print("📋 当前配置:")
    print(f"- 应用名称: {settings.app_name}")
    print(f"- 应用版本: {settings.app_version}")
    print(f"- API地址: {settings.api_base_url}")
    print(f"- API版本: {settings.api_version}")
    print(f"- 窗口大小: {settings.window_width}x{settings.window_height}")
    print(f"- 全屏模式: {'是' if settings.fullscreen else '否'}")
    print(f"- 主题: {settings.theme}")
    
    print("\n⚙️ 配置文件位置:")
    print("- config/app_config.json")
    print("- config/settings.py")
    print("- config/GrilmorphismUI.json")

def demo_features():
    """演示系统功能"""
    print("\n=== 系统功能演示 ===\n")
    
    features = [
        ("🏫 学校用户功能", [
            "系统登录 - 安全的用户认证",
            "食谱管理 - 食谱的增删改查",
            "采购下单 - 食材采购管理",
            "订单管理 - 订单跟踪和收货",
            "仓储管理 - 库存和出入库",
            "上报管理 - 数据统计报表",
            "答疑解惑 - 在线客服支持",
            "码上举报 - 二维码举报功能",
            "大屏展示 - 数据可视化"
        ]),
        ("🔌 API接口支持", [
            "登录认证接口",
            "仓库列表接口",
            "项目列表接口",
            "餐别列表接口",
            "库存管理接口",
            "出入库接口",
            "打印记录接口"
        ]),
        ("🔧 硬件接入", [
            "晨检仪设备接入",
            "留样秤数据采集",
            "出入库台秤集成"
        ]),
        ("🏪 供应商功能", [
            "供应商食谱管理",
            "在线结算系统",
            "动态价格更新"
        ])
    ]
    
    for category, items in features:
        print(f"{category}:")
        for item in items:
            print(f"  • {item}")
        print()

def main():
    """主演示函数"""
    print("🍽️ 智慧食堂管理系统演示")
    print("Smart Canteen Management System Demo")
    print("=" * 50)
    
    # 演示配置系统
    demo_config_system()
    
    # 演示UI组件
    demo_ui_components()
    
    # 演示系统功能
    demo_features()
    
    # 演示API使用（注释掉，因为需要真实的API服务器）
    # demo_api_usage()
    
    print("\n🚀 要启动完整的GUI应用程序，请运行:")
    print("python main.py")
    print("\n或使用启动脚本:")
    print("python run.py")

if __name__ == "__main__":
    main()
