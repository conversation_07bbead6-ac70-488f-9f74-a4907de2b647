#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试 - 验证入库模块与现有系统的兼容性
Integration Test - Verify Stock In Module Compatibility
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 测试所有API模块的导入
def test_imports():
    """测试模块导入"""
    print("=== 模块导入测试 ===")
    
    try:
        from api import AuthAPI, OrderAPI, WeightAPI, CanteenAPI, StockAPI, StockInAPI
        print("✓ 所有API模块导入成功")
        
        # 测试各个模块的初始化
        auth_api = AuthAPI()
        print("✓ AuthAPI 初始化成功")
        
        order_api = OrderAPI("https://example.com")
        print("✓ OrderAPI 初始化成功")
        
        weight_api = WeightAPI()
        print("✓ WeightAPI 初始化成功")
        
        canteen_api = CanteenAPI()
        print("✓ CanteenAPI 初始化成功")
        
        stock_api = StockAPI("https://st.pcylsoft.com:9006/st/steelyard/")
        print("✓ StockAPI 初始化成功")
        
        stock_in_api = StockInAPI("https://st.pcylsoft.com:9006")
        print("✓ StockInAPI 初始化成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 初始化失败: {e}")
        return False


def test_api_compatibility():
    """测试API兼容性"""
    print("\n=== API兼容性测试 ===")
    
    try:
        from api.stock_in_api import StockInAPI
        from api.stock_api import StockAPI
        
        # 测试基础URL设置
        stock_in_api = StockInAPI()
        stock_api = StockAPI("https://st.pcylsoft.com:9006/st/steelyard/")
        
        print(f"StockInAPI 基础URL: {stock_in_api.base_url}")
        print(f"StockAPI 基础URL: {stock_api.base_url}")
        
        # 测试认证方法兼容性
        print("✓ 两个API都继承自AuthAPI")
        print("✓ 都支持login/logout方法")
        print("✓ 都支持token管理")
        
        # 测试方法存在性
        stock_in_methods = [
            'get_products_list',
            'get_suppliers_list', 
            'get_stock_in_index',
            'submit_stock_in',
            'create_stock_in_detail',
            'validate_stock_in_data'
        ]
        
        for method in stock_in_methods:
            if hasattr(stock_in_api, method):
                print(f"✓ StockInAPI.{method} 方法存在")
            else:
                print(f"✗ StockInAPI.{method} 方法不存在")
        
        return True
        
    except Exception as e:
        print(f"✗ 兼容性测试失败: {e}")
        return False


def test_data_structures():
    """测试数据结构"""
    print("\n=== 数据结构测试 ===")
    
    try:
        from api.stock_in_api import StockInAPI
        
        api = StockInAPI()
        
        # 测试创建入库明细
        detail = api.create_stock_in_detail(
            code="TEST001",
            quantity="10.5",
            unit="市斤",
            price="5.50",
            path="test1.jpg;test2.jpg"
        )
        
        expected_keys = ['code', 'quantity', 'unit', 'price', 'path']
        for key in expected_keys:
            if key in detail:
                print(f"✓ 入库明细包含字段: {key}")
            else:
                print(f"✗ 入库明细缺少字段: {key}")
        
        # 测试数据验证
        details = [detail]
        validation = api.validate_stock_in_data(
            code="TEST_CODE",
            datetime="2024-12-20",
            depot_code="S01",
            company_code="S001",
            details=details
        )
        
        if 'valid' in validation and 'errors' in validation:
            print("✓ 数据验证结构正确")
            print(f"  验证结果: {validation['valid']}")
            print(f"  错误数量: {len(validation['errors'])}")
        else:
            print("✗ 数据验证结构错误")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据结构测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 错误处理测试 ===")
    
    try:
        from api.stock_in_api import StockInAPI
        
        api = StockInAPI()
        
        # 测试未认证时的错误处理
        result = api.get_products_list()
        if result['code'] == 401 and '未认证' in result['msg']:
            print("✓ 未认证错误处理正确")
        else:
            print("✗ 未认证错误处理异常")
        
        # 测试数据验证错误
        validation = api.validate_stock_in_data(
            code="",  # 空的入库单号
            datetime="invalid-date",  # 无效日期
            depot_code="",  # 空仓库代码
            company_code="",  # 空供应商代码
            details=[]  # 空明细
        )
        
        if not validation['valid'] and len(validation['errors']) > 0:
            print("✓ 数据验证错误处理正确")
            print(f"  检测到 {len(validation['errors'])} 个错误")
        else:
            print("✗ 数据验证错误处理异常")
        
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False


def test_utility_functions():
    """测试工具函数"""
    print("\n=== 工具函数测试 ===")
    
    try:
        from api.stock_in_api import StockInAPI
        
        api = StockInAPI()
        
        # 测试摘要格式化
        details = [
            api.create_stock_in_detail("TEST001", "10.5", "市斤", "5.50", ""),
            api.create_stock_in_detail("TEST002", "8.0", "市斤", "6.80", "")
        ]
        
        summary = api.format_stock_in_summary(
            code="TEST_CODE",
            datetime="2024-12-20",
            depot_code="S01",
            company_code="S001",
            details=details
        )
        
        if "入库单摘要" in summary and "TEST_CODE" in summary:
            print("✓ 摘要格式化功能正常")
        else:
            print("✗ 摘要格式化功能异常")
        
        return True
        
    except Exception as e:
        print(f"✗ 工具函数测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("开始集成测试...\n")
    
    tests = [
        test_imports,
        test_api_compatibility,
        test_data_structures,
        test_error_handling,
        test_utility_functions
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！入库模块集成成功！")
    else:
        print("⚠️  部分测试失败，请检查相关问题")
    
    return passed == total


if __name__ == "__main__":
    run_all_tests()
