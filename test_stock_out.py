#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
出库管理模块测试脚本
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtCore import Qt

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from ui.modules.stock_out_module import StockOutModule
    from api.stock_api import StockAPI
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    sys.exit(1)


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("出库管理模块测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置背景
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #1e3a8a,
                    stop: 0.5 #3730a3,
                    stop: 1 #581c87
                );
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建出库管理模块
        self.stock_out_module = StockOutModule()
        layout.addWidget(self.stock_out_module)
        
        # 创建测试API（可选）
        try:
            api = StockAPI("http://localhost:8000")  # 测试URL
            self.stock_out_module.set_api(api)
            print("✅ API设置成功")
        except Exception as e:
            print(f"⚠️ API设置失败（这是正常的，如果没有运行服务器）: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("出库管理测试")
    app.setApplicationVersion("1.0.0")
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    print("🚀 出库管理模块测试启动")
    print("📝 测试说明:")
    print("   1. 界面应该显示出库管理的完整UI")
    print("   2. 左侧有基础信息选择和库存商品列表")
    print("   3. 右侧有选中商品、重量和照片功能")
    print("   4. 底部有重置、预览和提交按钮")
    print("   5. 如果没有API连接，数据加载会失败（这是正常的）")
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
