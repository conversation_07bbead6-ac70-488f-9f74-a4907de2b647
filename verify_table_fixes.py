#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证表格修复 - 检查订单列表行高和序号显示修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_order_module_fixes():
    """验证订单模块的表格修复"""
    
    print("🔍 验证订单列表表格修复")
    print("=" * 50)
    
    try:
        # 检查OrderModule是否可以正常导入
        from ui.modules.order_module import OrderModule
        print("✅ OrderModule 导入成功")
        
        # 检查关键修复点
        fixes_verified = []
        
        # 1. 检查文件中是否包含行高设置
        with open('ui/modules/order_module.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查垂直表头设置
            if 'verticalHeader().setDefaultSectionSize(45)' in content:
                fixes_verified.append("✅ 默认行高设置为45px")
            else:
                fixes_verified.append("❌ 缺少默认行高设置")
            
            # 检查垂直表头可见性
            if 'vertical_header.setVisible(True)' in content:
                fixes_verified.append("✅ 垂直表头（序号）设置为可见")
            else:
                fixes_verified.append("❌ 缺少垂直表头可见性设置")
            
            # 检查垂直表头样式
            if 'QHeaderView::section:vertical' in content:
                fixes_verified.append("✅ 垂直表头样式已定义")
            else:
                fixes_verified.append("❌ 缺少垂直表头样式")
            
            # 检查行高在填充数据时的设置
            if 'setRowHeight(i, 45)' in content:
                fixes_verified.append("✅ 数据填充时设置行高")
            else:
                fixes_verified.append("❌ 缺少数据填充时的行高设置")
            
            # 检查表格项垂直居中对齐
            if 'AlignVCenter' in content:
                fixes_verified.append("✅ 表格项垂直居中对齐")
            else:
                fixes_verified.append("❌ 缺少表格项垂直居中对齐")
            
            # 检查表格项最小高度
            if 'min-height: 25px' in content:
                fixes_verified.append("✅ 表格项最小高度设置")
            else:
                fixes_verified.append("❌ 缺少表格项最小高度设置")
        
        print("\n📋 修复验证结果：")
        for fix in fixes_verified:
            print(f"  {fix}")
        
        # 统计修复完成情况
        completed_fixes = len([f for f in fixes_verified if f.startswith("✅")])
        total_fixes = len(fixes_verified)
        
        print(f"\n📊 修复完成度：{completed_fixes}/{total_fixes} ({completed_fixes/total_fixes*100:.1f}%)")
        
        if completed_fixes == total_fixes:
            print("\n🎉 所有表格行高和序号显示修复已完成！")
            print("\n🔧 主要修复内容：")
            print("1. 设置默认行高为45px，确保内容有足够空间")
            print("2. 启用垂直表头显示，显示行序号（1,2,3...）")
            print("3. 设置垂直表头样式，确保序号清晰可见")
            print("4. 在填充数据时为每行设置45px高度")
            print("5. 所有表格项设置垂直居中对齐")
            print("6. 表格项设置最小高度25px")
            print("\n✨ 现在订单列表中的序号应该完整显示，行高足够，内容垂直居中！")
        else:
            print(f"\n⚠️  还有 {total_fixes - completed_fixes} 个修复项需要完善")
        
        return completed_fixes == total_fixes
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

def main():
    """主函数"""
    success = verify_order_module_fixes()
    
    if success:
        print("\n🚀 建议测试步骤：")
        print("1. 运行主程序：python main.py")
        print("2. 进入订单管理页面")
        print("3. 检查订单列表左侧序号列是否完整显示")
        print("4. 检查每行高度是否足够，内容是否垂直居中")
        print("5. 加载更多订单数据测试序号显示（1,2,3...10,11...）")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
