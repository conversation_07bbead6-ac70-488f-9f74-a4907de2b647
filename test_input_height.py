#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输入框高度测试
Input Height Test
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_input_height():
    """测试输入框高度"""
    print("=" * 60)
    print("📏 输入框高度修复测试")
    print("=" * 60)
    
    try:
        from PyQt6.QtWidgets import QApplication, QLineEdit, QWidget, QVBoxLayout, QLabel
        from ui.styles import GlassmorphismStyles
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建测试窗口
        window = QWidget()
        window.setWindowTitle("输入框高度测试")
        window.setFixedSize(500, 400)
        
        layout = QVBoxLayout(window)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 添加说明标签
        info_label = QLabel("输入框高度修复测试\n请检查输入框是否完整显示")
        info_label.setStyleSheet("""
        QLabel {
            color: white;
            font-size: 16px;
            font-weight: 600;
            background: transparent;
            border: none;
            padding: 10px;
        }
        """)
        layout.addWidget(info_label)
        
        # 创建测试输入框1 - 用户名
        username_label = QLabel("用户名输入框:")
        username_label.setStyleSheet("color: white; font-size: 14px; font-weight: 500;")
        
        username_input = QLineEdit()
        username_input.setPlaceholderText("请输入用户名 - 测试文字显示")
        username_input.setMinimumHeight(60)  # 与修复后的设置一致
        username_input.setStyleSheet(GlassmorphismStyles.get_input_style())
        username_input.setText("测试用户名123456")  # 添加测试文字
        
        # 创建测试输入框2 - 密码
        password_label = QLabel("密码输入框:")
        password_label.setStyleSheet("color: white; font-size: 14px; font-weight: 500;")
        
        password_input = QLineEdit()
        password_input.setPlaceholderText("请输入密码 - 测试文字显示")
        password_input.setMinimumHeight(60)  # 与修复后的设置一致
        password_input.setStyleSheet(GlassmorphismStyles.get_input_style())
        password_input.setText("测试密码abcdef")  # 添加测试文字
        
        # 创建测试输入框3 - 长文本
        long_label = QLabel("长文本测试:")
        long_label.setStyleSheet("color: white; font-size: 14px; font-weight: 500;")
        
        long_input = QLineEdit()
        long_input.setPlaceholderText("长文本显示测试")
        long_input.setMinimumHeight(60)
        long_input.setStyleSheet(GlassmorphismStyles.get_input_style())
        long_input.setText("这是一个很长的测试文本，用来检查输入框是否能完整显示所有内容")
        
        # 添加到布局
        layout.addWidget(username_label)
        layout.addWidget(username_input)
        layout.addWidget(password_label)
        layout.addWidget(password_input)
        layout.addWidget(long_label)
        layout.addWidget(long_input)
        
        # 添加说明
        help_label = QLabel("""
测试要点:
• 输入框高度应该足够显示完整文字
• 文字不应该被截断或只显示一半
• 圆角应该是16px，边框清晰
• 点击时应该有紫色边框和阴影效果
        """)
        help_label.setStyleSheet("""
        QLabel {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px;
        }
        """)
        layout.addWidget(help_label)
        
        # 设置窗口背景
        window.setStyleSheet("""
        QWidget {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(30, 30, 60, 1),
                stop:0.5 rgba(60, 30, 90, 1),
                stop:1 rgba(90, 60, 120, 1));
        }
        """)
        
        print("✅ 输入框高度测试窗口创建成功")
        print("📋 修复内容:")
        print("  • 输入框最小高度设置为60px")
        print("  • 移除最大高度限制，允许自适应")
        print("  • 使用setMinimumHeight而不是setFixedHeight")
        print("  • 确保文字完整显示")
        
        print("\n💡 测试说明:")
        print("  1. 检查输入框是否完整显示文字")
        print("  2. 文字不应该被截断或只显示一半")
        print("  3. 输入框应该有统一的圆边样式")
        print("  4. 点击输入框时应该有紫色边框效果")
        
        window.show()
        
        print("\n🖼️ 测试窗口已显示，请检查输入框显示效果")
        print("按Ctrl+C退出测试")
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_fix_summary():
    """显示修复总结"""
    print("\n" + "=" * 60)
    print("🔧 输入框高度修复总结")
    print("=" * 60)
    
    print("🎯 问题:")
    print("  • 输入框只显示了一半")
    print("  • 文字被截断，显示不完整")
    
    print("\n✅ 修复方案:")
    print("  • 将setFixedHeight(56)改为setMinimumHeight(60)")
    print("  • 移除样式中的max-height限制")
    print("  • 保持min-height: 60px确保最小高度")
    print("  • 允许输入框根据内容自适应高度")
    
    print("\n📁 修改的文件:")
    print("  • ui/login_window.py - 输入框高度设置")
    print("  • ui/styles.py - 样式高度限制")
    
    print("\n🎨 视觉效果:")
    print("  • 输入框高度足够显示完整文字")
    print("  • 保持统一的圆边样式(16px)")
    print("  • 焦点效果正常显示")
    print("  • 符合玻璃态设计风格")

def main():
    """主函数"""
    print("🍽️ 智慧食堂管理系统 - 输入框高度修复测试")
    
    # 显示修复总结
    show_fix_summary()
    
    print("\n" + "=" * 60)
    print("🚀 启动高度测试")
    print("=" * 60)
    
    # 运行测试
    result = test_input_height()
    
    if result:
        print("\n✅ 高度测试完成")
    else:
        print("\n❌ 高度测试失败")

if __name__ == "__main__":
    main()
