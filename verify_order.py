#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单功能验证脚本
Order Feature Verification Script
"""

print("开始验证订单功能...")

# 检查文件是否存在
import os

files_to_check = [
    "api/order_api.py",
    "ui/modules/order_module.py",
    "api/auth_api.py",
    "config/settings.py"
]

print("\n检查文件存在性:")
for file_path in files_to_check:
    if os.path.exists(file_path):
        print(f"✅ {file_path}")
    else:
        print(f"❌ {file_path}")

# 检查语法
print("\n检查语法:")
try:
    import ast
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                ast.parse(content)
                print(f"✅ {file_path} 语法正确")
            except SyntaxError as e:
                print(f"❌ {file_path} 语法错误: {e}")
            except Exception as e:
                print(f"⚠️ {file_path} 检查失败: {e}")
                
except Exception as e:
    print(f"语法检查失败: {e}")

print("\n✅ 订单功能验证完成！")
