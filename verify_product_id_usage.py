#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证商品ID使用的正确性
Verify Product ID Usage
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_product_id_usage():
    """验证商品ID使用的正确性"""
    print("🔍 验证商品ID使用的正确性")
    print("=" * 60)
    
    # 根据API响应数据的正确格式
    correct_product_data = {
        "id": 194933,  # 商品ID - 用于重量提交API
        "spec": None,
        "code": "13010501",  # 商品编码 - 用于显示
        "name": "菠萝",
        "buy_unit": "市斤",
        "buy_quantity": "1",
        "stock_quantity": 3,
        "repair_receive": None,
        "deliver_unit": "市斤",
        "deliver_quantity": "3.11",
        "receive_quantity": None,
        "stock_mode": 1,  # 入库模式：1=入库
        "receive_date": None,
        "path": None,
        "batch": None
    }
    
    print("📋 正确的商品数据格式:")
    print(f"  商品ID (用于API): {correct_product_data['id']}")
    print(f"  商品编码 (用于显示): {correct_product_data['code']}")
    print(f"  商品名称: {correct_product_data['name']}")
    print(f"  入库模式: {correct_product_data['stock_mode']}")
    
    # 验证重量提交模块
    try:
        from ui.modules.weight_submission_module import WeightSubmissionModule
        
        print("\n🔧 测试重量提交模块...")
        
        # 创建模块实例
        weight_module = WeightSubmissionModule()
        
        # 设置商品信息
        weight_module.set_product_info(correct_product_data)
        
        # 验证商品信息设置
        if weight_module.current_product:
            stored_id = weight_module.current_product.get('id')
            stored_code = weight_module.current_product.get('code')
            stored_name = weight_module.current_product.get('name')
            stored_stock_mode = weight_module.current_product.get('stock_mode')
            
            print("✅ 商品信息设置成功")
            print(f"  存储的商品ID: {stored_id}")
            print(f"  存储的商品编码: {stored_code}")
            print(f"  存储的商品名称: {stored_name}")
            print(f"  存储的入库模式: {stored_stock_mode}")
            
            # 验证ID是否正确
            if stored_id == 194933:
                print("✅ 商品ID正确 (194933)")
            else:
                print(f"❌ 商品ID错误，期望: 194933, 实际: {stored_id}")
            
            # 验证编码是否正确
            if stored_code == "13010501":
                print("✅ 商品编码正确 (13010501)")
            else:
                print(f"❌ 商品编码错误，期望: 13010501, 实际: {stored_code}")
                
        else:
            print("❌ 商品信息设置失败")
            
    except Exception as e:
        print(f"❌ 重量提交模块测试失败: {e}")
    
    # 验证API提交参数
    try:
        from api.weight_api import WeightAPI
        
        print("\n🔧 测试重量提交API...")
        
        # 创建API实例
        api = WeightAPI("https://st.pcylsoft.com:9006/st/steelyard/")
        
        # 模拟API调用参数
        test_params = {
            'id': correct_product_data['id'],  # 使用商品ID
            'quantity': '3.11,3.15,3.08',
            'stock_mode': correct_product_data['stock_mode'],
            'type': 0
        }
        
        print("✅ API实例创建成功")
        print("📤 重量提交API参数:")
        print(f"  id: {test_params['id']} (商品ID)")
        print(f"  quantity: {test_params['quantity']}")
        print(f"  stock_mode: {test_params['stock_mode']}")
        print(f"  type: {test_params['type']}")
        
        # 验证参数正确性
        if test_params['id'] == 194933:
            print("✅ API参数中的ID正确")
        else:
            print(f"❌ API参数中的ID错误")
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
    
    # 验证图片提交API参数
    try:
        print("\n🔧 测试图片提交API...")
        
        # 模拟图片提交参数
        picture_params = {
            'order_detail_id': correct_product_data['id'],  # 使用商品ID
            'batch': 0,
            'image_path': 'photos/test_photo.jpg'
        }
        
        print("📸 图片提交API参数:")
        print(f"  order_detail_id: {picture_params['order_detail_id']} (商品ID)")
        print(f"  batch: {picture_params['batch']}")
        print(f"  image_path: {picture_params['image_path']}")
        
        if picture_params['order_detail_id'] == 194933:
            print("✅ 图片提交API参数中的ID正确")
        else:
            print("❌ 图片提交API参数中的ID错误")
            
    except Exception as e:
        print(f"❌ 图片API测试失败: {e}")
    
    print("\n📖 总结:")
    print("=" * 60)
    print("✅ 正确做法:")
    print("  1. 从订单详情API获取商品数据")
    print("  2. 使用商品的 'id' 字段 (194933) 进行重量和图片提交")
    print("  3. 使用商品的 'code' 字段 (13010501) 进行界面显示")
    print("  4. 使用商品的 'name' 字段进行用户识别")
    print()
    print("🔗 API接口映射:")
    print("  订单详情: GET /st/steelyard/?op=order_item")
    print("  重量提交: POST /st/steelyard/?op=weight (使用商品ID)")
    print("  图片提交: POST /st/steelyard/?op=picture (使用商品ID)")
    print()
    print("📋 数据流程:")
    print("  1. 获取订单详情 → 商品列表")
    print("  2. 选择商品 → 传递完整商品数据对象")
    print("  3. 重量提交 → 使用商品ID (194933)")
    print("  4. 图片提交 → 使用商品ID (194933)")

if __name__ == "__main__":
    verify_product_id_usage()
