#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
入库API测试文件
Test file for Stock In API
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.stock_in_api import StockInAPI


def test_stock_in_api():
    """测试入库API功能"""
    
    # 初始化API
    api = StockInAPI("https://st.pcylsoft.com:9006")
    
    print("=== 入库API测试 ===")
    print(f"API基础URL: {api.base_url}")
    
    # 注意：实际使用时需要先登录获取token
    # api.login("username", "password")
    # 或者直接设置token
    # api.set_access_token("your_token_here")
    
    print("\n1. 测试货品列表接口")
    print("调用: api.get_products_list()")
    print("说明: 获取所有可入库的货品列表")
    
    print("\n2. 测试供应商列表接口")
    print("调用: api.get_suppliers_list()")
    print("说明: 获取所有供应商列表")
    
    print("\n3. 测试入库单号获取接口")
    print("调用: api.get_stock_in_index()")
    print("说明: 获取新的入库单号")
    
    print("\n4. 测试入库明细创建")
    detail = api.create_stock_in_detail(
        code="13010405",
        quantity="10.5",
        unit="市斤",
        price="5.50",
        path="images/product1.jpg;images/product2.jpg"
    )
    print(f"创建的入库明细: {detail}")
    
    print("\n5. 测试数据验证")
    details = [detail]
    validation = api.validate_stock_in_data(
        code="YI25073117941",
        datetime="2024-12-20",
        depot_code="S01",
        company_code="S001",
        details=details
    )
    print(f"数据验证结果: {validation}")
    
    print("\n6. 测试入库单摘要格式化")
    summary = api.format_stock_in_summary(
        code="YI25073117941",
        datetime="2024-12-20",
        depot_code="S01",
        company_code="S001",
        details=details
    )
    print(f"入库单摘要:\n{summary}")
    
    print("\n7. 测试入库提交接口（需要有效token）")
    print("调用: api.submit_stock_in(code, datetime, depot_code, company_code, details)")
    print("说明: 提交入库申请")
    
    print("\n=== 测试完成 ===")
    print("\n使用说明:")
    print("1. 首先调用 api.login(username, password) 进行登录")
    print("2. 或者直接设置token: api.set_access_token(token)")
    print("3. 调用 api.get_products_list() 获取货品列表")
    print("4. 调用 api.get_suppliers_list() 获取供应商列表")
    print("5. 调用 api.get_stock_in_index() 获取入库单号")
    print("6. 使用 api.create_stock_in_detail() 创建入库明细")
    print("7. 使用 api.validate_stock_in_data() 验证数据")
    print("8. 调用 api.submit_stock_in() 提交入库申请")


def demo_complete_workflow():
    """演示完整的入库流程"""
    
    print("\n=== 完整入库流程演示 ===")
    
    # 初始化API
    api = StockInAPI()
    
    # 步骤1: 登录（示例）
    print("步骤1: 登录")
    print("api.login('username', 'password')")
    
    # 步骤2: 获取货品列表
    print("\n步骤2: 获取货品列表")
    print("products = api.get_products_list()")
    
    # 步骤3: 获取供应商列表
    print("\n步骤3: 获取供应商列表")
    print("suppliers = api.get_suppliers_list()")
    
    # 步骤4: 获取入库单号
    print("\n步骤4: 获取入库单号")
    print("stock_in_index = api.get_stock_in_index()")
    print("code = stock_in_index['msg']  # 入库单号")
    
    # 步骤5: 创建入库明细
    print("\n步骤5: 创建入库明细")
    print("details = []")
    print("detail1 = api.create_stock_in_detail(")
    print("    code='13010405',")
    print("    quantity='10.5',")
    print("    unit='市斤',")
    print("    price='5.50',")
    print("    path='images/product1.jpg'")
    print(")")
    print("details.append(detail1)")
    
    # 步骤6: 验证数据
    print("\n步骤6: 验证数据")
    print("validation = api.validate_stock_in_data(")
    print("    code=code,")
    print("    datetime='2024-12-20',")
    print("    depot_code='S01',")
    print("    company_code='S001',")
    print("    details=details")
    print(")")
    print("if validation['valid']:")
    print("    # 数据有效，继续提交")
    print("else:")
    print("    # 数据无效，显示错误信息")
    print("    print(validation['errors'])")
    
    # 步骤7: 提交入库申请
    print("\n步骤7: 提交入库申请")
    print("result = api.submit_stock_in(")
    print("    code=code,")
    print("    datetime='2024-12-20',")
    print("    depot_code='S01',")
    print("    company_code='S001',")
    print("    details=details")
    print(")")
    print("if result['code'] == 200:")
    print("    print('入库成功!')")
    print("else:")
    print("    print(f'入库失败: {result[\"msg\"]}')")


if __name__ == "__main__":
    test_stock_in_api()
    demo_complete_workflow()
