# 智慧食堂管理系统 - 使用说明

## 快速启动

### 方法一：使用批处理文件（推荐）
双击 `start.bat` 文件，系统会自动检查环境并启动程序。

### 方法二：命令行启动
```bash
# 1. 安装依赖
pip install PyQt6 requests

# 2. 启动程序
python main.py
```

### 方法三：使用启动脚本
```bash
python run.py
```

## 故障排除

### 问题1：AttributeError: 'ApplicationAttribute' has no attribute 'AA_EnableHighDpiScaling'
**解决方案**：这是PyQt6版本兼容性问题，已在最新版本中修复。

### 问题2：ImportError: No module named 'PyQt6'
**解决方案**：
```bash
pip install PyQt6
```

### 问题3：ImportError: No module named 'requests'
**解决方案**：
```bash
pip install requests
```

### 问题4：程序启动后没有界面显示
**可能原因**：
1. 程序正在后台运行，检查任务管理器
2. 窗口可能在屏幕外，尝试Alt+Tab切换
3. 显示器分辨率问题

**解决方案**：
1. 结束所有Python进程后重新启动
2. 修改配置文件 `config/app_config.json` 中的窗口设置
3. 使用调试模式：`python debug_main.py`

### 问题5：字体显示异常
**解决方案**：
系统会自动回退到默认字体，不影响功能使用。

## 系统要求

- **操作系统**：Windows 10/11（推荐）
- **Python版本**：3.8 或更高版本
- **内存**：至少 4GB RAM
- **显示器**：1024x768 或更高分辨率

## 配置说明

### API配置
编辑 `config/app_config.json` 文件：
```json
{
  "api": {
    "base_url": "https://your-api-server.com/st/steelyard/",
    "timeout": 10,
    "version": "1.8.13"
  }
}
```

### 界面配置
```json
{
  "ui": {
    "window_width": 1200,
    "window_height": 800,
    "fullscreen": false,
    "theme": "glassmorphism"
  }
}
```

## 功能说明

### 登录界面
- 输入用户名和密码
- 支持回车键快速登录
- **新增**: Token过期检查和自动登录
- **新增**: 历史账号记忆和快速选择
- **新增**: 记住账号选项
- **新增**: Token状态显示
- 网络错误提示

### 主界面
- 侧边栏导航菜单
- 功能卡片快速入口
- 多页面切换系统
- 退出登录功能

### 食谱管理
- 食谱列表查看
- 新建/编辑/删除食谱
- 搜索和过滤功能
- 食材清单管理

## 开发模式

### 调试启动
```bash
python debug_main.py
```

### API测试
```bash
python test_api.py
```

### 功能演示
```bash
python demo.py
```

### 简单测试
```bash
python test_simple.py
```

## 新功能使用说明

### Token自动登录
- 首次登录时勾选"记住账号"
- 系统会保存Token和用户信息
- 下次启动时自动检查Token有效性
- Token有效则直接进入主界面
- Token过期则显示登录界面并提示

### 历史账号选择
- 点击登录界面的"历史账号"按钮
- 查看之前登录过的所有账号
- 显示登录次数和最后登录时间
- 双击账号快速选择
- 可以删除不需要的历史记录

### 认证信息管理
- 认证信息存储在 `config/auth_storage.json`
- 包含当前Token和历史登录记录
- 支持Token过期时间显示
- 自动清理无效的认证信息

## 常见问题

**Q: 如何修改API服务器地址？**
A: 编辑 `config/app_config.json` 文件中的 `api.base_url` 字段。

**Q: 如何切换到窗口模式？**
A: 在配置文件中设置 `ui.fullscreen` 为 `false`。

**Q: 登录失败怎么办？**
A: 检查网络连接和API服务器状态，确认用户名密码正确。

**Q: Token过期了怎么办？**
A: 系统会自动检测并提示重新登录，输入密码即可。

**Q: 如何清除历史账号？**
A: 在历史账号对话框中选择账号并点击删除按钮。

**Q: 界面显示异常怎么办？**
A: 尝试更新显卡驱动，或在配置中禁用硬件加速。

**Q: 如何添加新功能模块？**
A: 参考 `ui/modules/recipe_module.py` 的实现方式。

## 技术支持

如遇到其他问题，请：
1. 查看控制台错误信息
2. 检查配置文件格式
3. 确认网络连接状态
4. 联系技术支持团队

## 更新日志

### v1.0.0
- 初始版本发布
- 登录系统实现
- 主界面框架完成
- 食谱管理模块
- API接口集成
- 玻璃态UI设计
